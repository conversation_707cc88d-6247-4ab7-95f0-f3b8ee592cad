// User Management Models for Chattrix Dashboard

// User Details interface matching backend UserDetails model
export interface UserDetails {
  id?: string;
  fullName?: string;
  email?: string;
  isActive: boolean;
  profileImageUrl?: string;
  roles: string[];
  phoneNumber?: string;
  description?: string;
  createdOn: Date;
}

// Pagination Parameters interface matching backend PaginationParameters
export interface PaginationParameters {
  pageNumber: number;
  pageSize: number;
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
  name?: string;
  role?: string;
  status?: string;
  createdFrom?: Date;
  createdTo?: Date;
}

// Paged Response interface matching backend PagedResponse
export interface PagedResponse<T> {
  items: T[];
  totalItems: number;
  currentPage: number;
  pageSize: number;
  totalPages: number;
}

// API Response interface matching backend Response model
export interface ApiResponse<T = any> {
  isSuccess: boolean;
  message: string;
  data?: T;
  errors?: any;
}

// User Management specific responses
export interface UserManagementResponse extends ApiResponse<UserDetails> {}
export interface UsersPagedResponse
  extends ApiResponse<PagedResponse<UserDetails>> {}

// Add User request interface matching backend AddUser model
export interface AddUserRequest {
  email: string;
  fullName: string;
  password: string;
  isActive: boolean;
  phoneNumber?: string;
  description?: string;
  profileImage?: File;
  profileImageUrl?: string;
  roles: string[];
}

// Update User request interface
export interface UpdateUserRequest {
  id?: string;
  fullName?: string;
  email?: string;
  isActive: boolean;
  phoneNumber?: string;
  description?: string;
  roles: string[];
}

// Delete User request interface matching backend ToggleStatusRequest
export interface DeleteUserRequest {
  reason?: string;
  isActive: boolean;
}

// Filter options for the UI
export interface UserFilterOptions {
  searchTerm: string;
  statusFilter: 'all' | 'active' | 'inactive';
  roleFilter: string;
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
}

// Table column configuration
export interface TableColumn {
  key: string;
  label: string;
  sortable: boolean;
  width?: string;
}

// Action permissions based on user roles
export interface UserActionPermissions {
  canView: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canChangeStatus: boolean;
}

// User table row data with computed properties
export interface UserTableRow extends UserDetails {
  displayRoles: string;
  statusDisplay: string;
  formattedCreatedOn: string;
  actions: UserActionPermissions;
}

// Modal states for user management
export type ModalState = 'closed' | 'view' | 'edit' | 'delete' | 'add';

// Sort configuration
export interface SortConfig {
  field: string;
  direction: 'asc' | 'desc';
}

// Loading states for different operations
export interface LoadingStates {
  fetchingUsers: boolean;
  deletingUser: boolean;
  updatingUser: boolean;
  addingUser: boolean;
}

// Error states
export interface ErrorStates {
  fetchError: string | null;
  deleteError: string | null;
  updateError: string | null;
  addError: string | null;
}
