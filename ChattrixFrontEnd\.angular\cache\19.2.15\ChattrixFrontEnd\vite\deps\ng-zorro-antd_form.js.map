{"version": 3, "sources": ["../../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-tooltip.mjs", "../../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-form.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/overlay';\nimport { OverlayModule } from '@angular/cdk/overlay';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, inject, ElementRef, ViewContainerRef, Renderer2, PLATFORM_ID, Directive, ChangeDetectorRef, ViewChild, TemplateRef, booleanAttribute, Output, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { zoomBigMotion } from 'ng-zorro-antd/core/animation';\nimport { isPresetColor } from 'ng-zorro-antd/core/color';\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport * as i2 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i3 from 'ng-zorro-antd/core/overlay';\nimport { POSITION_MAP, DEFAULT_TOOLTIP_POSITIONS, getPlacementName, NzOverlayModule } from 'ng-zorro-antd/core/overlay';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { _getEventTarget } from '@angular/cdk/platform';\nimport { isPlatformBrowser } from '@angular/common';\nimport { Subject, asapScheduler } from 'rxjs';\nimport { distinctUntilChanged, takeUntil, filter, delay } from 'rxjs/operators';\nimport { NzConfigService } from 'ng-zorro-antd/core/config';\nimport { toBoolean, isNotNil } from 'ng-zorro-antd/core/util';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"overlay\"];\nfunction NzToolTipComponent_ng_template_0_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.nzTitle);\n  }\n}\nfunction NzToolTipComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n    i0.ɵɵelement(3, \"span\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 6);\n    i0.ɵɵtemplate(5, NzToolTipComponent_ng_template_0_ng_container_5_Template, 2, 1, \"ng-container\", 7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(ctx_r1.nzOverlayStyle);\n    i0.ɵɵclassMap(ctx_r1._classMap);\n    i0.ɵɵclassProp(\"ant-tooltip-rtl\", ctx_r1.dir === \"rtl\");\n    i0.ɵɵproperty(\"@.disabled\", !!(ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation))(\"nzNoAnimation\", ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation)(\"@zoomBigMotion\", \"active\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleMap(ctx_r1._contentStyleMap);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(ctx_r1._contentStyleMap);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r1.nzTitle)(\"nzStringTemplateOutletContext\", ctx_r1.nzTitleContext);\n  }\n}\nclass NzTooltipBaseDirective {\n  componentType;\n  config;\n  cdkConnectedOverlayPush;\n  visibleChange = new EventEmitter();\n  /**\n   * This true title that would be used in other parts on this component.\n   */\n  get _title() {\n    return this.title || this.directiveTitle || null;\n  }\n  get _content() {\n    return this.content || this.directiveContent || null;\n  }\n  get _trigger() {\n    return typeof this.trigger !== 'undefined' ? this.trigger : 'hover';\n  }\n  get _placement() {\n    const p = this.placement;\n    return Array.isArray(p) && p.length > 0 ? p : typeof p === 'string' && p ? [p] : ['top'];\n  }\n  get _visible() {\n    return (typeof this.visible !== 'undefined' ? this.visible : this.internalVisible) || false;\n  }\n  get _mouseEnterDelay() {\n    return this.mouseEnterDelay || 0.15;\n  }\n  get _mouseLeaveDelay() {\n    return this.mouseLeaveDelay || 0.1;\n  }\n  get _overlayClassName() {\n    return this.overlayClassName || null;\n  }\n  get _overlayStyle() {\n    return this.overlayStyle || null;\n  }\n  get _overlayClickable() {\n    return this.overlayClickable ?? true;\n  }\n  internalVisible = false;\n  getProxyPropertyMap() {\n    return {\n      noAnimation: ['noAnimation', () => !!this.noAnimation]\n    };\n  }\n  component;\n  destroy$ = new Subject();\n  triggerDisposables = [];\n  delayTimer;\n  elementRef = inject(ElementRef);\n  hostView = inject(ViewContainerRef);\n  renderer = inject(Renderer2);\n  noAnimation = inject(NzNoAnimationDirective, {\n    host: true,\n    optional: true\n  });\n  nzConfigService = inject(NzConfigService);\n  platformId = inject(PLATFORM_ID);\n  constructor(componentType) {\n    this.componentType = componentType;\n  }\n  ngAfterViewInit() {\n    if (isPlatformBrowser(this.platformId)) {\n      this.createComponent();\n      this.registerTriggers();\n    }\n  }\n  ngOnChanges(changes) {\n    const {\n      trigger\n    } = changes;\n    if (trigger && !trigger.isFirstChange()) {\n      this.registerTriggers();\n    }\n    if (this.component) {\n      this.updatePropertiesByChanges(changes);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    // Clear toggling timer. Issue #3875 #4317 #4386\n    this.clearTogglingTimer();\n    this.removeTriggerListeners();\n  }\n  show() {\n    this.component?.show();\n  }\n  hide() {\n    this.component?.hide();\n  }\n  /**\n   * Force the component to update its position.\n   */\n  updatePosition() {\n    if (this.component) {\n      this.component.updatePosition();\n    }\n  }\n  /**\n   * Create a dynamic tooltip component. This method can be override.\n   */\n  createComponent() {\n    const componentRef = this.hostView.createComponent(this.componentType);\n    this.component = componentRef.instance;\n    // Remove the component's DOM because it should be in the overlay container.\n    this.renderer.removeChild(this.renderer.parentNode(this.elementRef.nativeElement), componentRef.location.nativeElement);\n    this.component.setOverlayOrigin(this.origin || this.elementRef);\n    this.initProperties();\n    const ngVisibleChange$ = this.component.nzVisibleChange.pipe(distinctUntilChanged());\n    ngVisibleChange$.pipe(takeUntil(this.destroy$)).subscribe(visible => {\n      this.internalVisible = visible;\n      this.visibleChange.emit(visible);\n    });\n    // In some cases, the rendering takes into account the height at which the `arrow` is in wrong place,\n    // so `cdk` sets the container position incorrectly.\n    // To avoid this, after placing the `arrow` in the correct position, we should `re-calculate` the position of the `overlay`.\n    ngVisibleChange$.pipe(filter(visible => visible), delay(0, asapScheduler), filter(() => Boolean(this.component?.overlay?.overlayRef)), takeUntil(this.destroy$)).subscribe(() => {\n      this.component?.updatePosition();\n    });\n  }\n  registerTriggers() {\n    // When the method gets invoked, all properties has been synced to the dynamic component.\n    // After removing the old API, we can just check the directive's own `nzTrigger`.\n    const el = this.elementRef.nativeElement;\n    const trigger = this.trigger;\n    this.removeTriggerListeners();\n    if (trigger === 'hover') {\n      let overlayElement;\n      this.triggerDisposables.push(this.renderer.listen(el, 'mouseenter', () => {\n        this.delayEnterLeave(true, true, this._mouseEnterDelay);\n      }));\n      this.triggerDisposables.push(this.renderer.listen(el, 'mouseleave', () => {\n        this.delayEnterLeave(true, false, this._mouseLeaveDelay);\n        if (this.component?.overlay.overlayRef && !overlayElement) {\n          overlayElement = this.component.overlay.overlayRef.overlayElement;\n          this.triggerDisposables.push(this.renderer.listen(overlayElement, 'mouseenter', () => {\n            this.delayEnterLeave(false, true, this._mouseEnterDelay);\n          }));\n          this.triggerDisposables.push(this.renderer.listen(overlayElement, 'mouseleave', () => {\n            this.delayEnterLeave(false, false, this._mouseLeaveDelay);\n          }));\n        }\n      }));\n    } else if (trigger === 'focus') {\n      this.triggerDisposables.push(this.renderer.listen(el, 'focusin', () => this.show()));\n      this.triggerDisposables.push(this.renderer.listen(el, 'focusout', () => this.hide()));\n    } else if (trigger === 'click') {\n      this.triggerDisposables.push(this.renderer.listen(el, 'click', e => {\n        e.preventDefault();\n        this.show();\n      }));\n    }\n    // Else do nothing because user wants to control the visibility programmatically.\n  }\n  updatePropertiesByChanges(changes) {\n    this.updatePropertiesByKeys(Object.keys(changes));\n  }\n  updatePropertiesByKeys(keys) {\n    const mappingProperties = {\n      // common mappings\n      title: ['nzTitle', () => this._title],\n      directiveTitle: ['nzTitle', () => this._title],\n      content: ['nzContent', () => this._content],\n      directiveContent: ['nzContent', () => this._content],\n      trigger: ['nzTrigger', () => this._trigger],\n      placement: ['nzPlacement', () => this._placement],\n      visible: ['nzVisible', () => this._visible],\n      mouseEnterDelay: ['nzMouseEnterDelay', () => this._mouseEnterDelay],\n      mouseLeaveDelay: ['nzMouseLeaveDelay', () => this._mouseLeaveDelay],\n      overlayClassName: ['nzOverlayClassName', () => this._overlayClassName],\n      overlayStyle: ['nzOverlayStyle', () => this._overlayStyle],\n      overlayClickable: ['nzOverlayClickable', () => this._overlayClickable],\n      arrowPointAtCenter: ['nzArrowPointAtCenter', () => this.arrowPointAtCenter],\n      cdkConnectedOverlayPush: ['cdkConnectedOverlayPush', () => this.cdkConnectedOverlayPush],\n      ...this.getProxyPropertyMap()\n    };\n    (keys || Object.keys(mappingProperties).filter(key => !key.startsWith('directive'))).forEach(property => {\n      if (mappingProperties[property]) {\n        const [name, valueFn] = mappingProperties[property];\n        this.updateComponentValue(name, valueFn());\n      }\n    });\n    this.component?.updateByDirective();\n  }\n  initProperties() {\n    this.updatePropertiesByKeys();\n  }\n  updateComponentValue(key, value) {\n    if (typeof value !== 'undefined') {\n      // @ts-ignore\n      this.component[key] = value;\n    }\n  }\n  delayEnterLeave(isOrigin, isEnter, delay = -1) {\n    if (this.delayTimer) {\n      this.clearTogglingTimer();\n    } else if (delay > 0) {\n      this.delayTimer = setTimeout(() => {\n        this.delayTimer = undefined;\n        isEnter ? this.show() : this.hide();\n      }, delay * 1000);\n    } else {\n      // `isOrigin` is used due to the tooltip will not hide immediately\n      // (may caused by the fade-out animation).\n      isEnter && isOrigin ? this.show() : this.hide();\n    }\n  }\n  removeTriggerListeners() {\n    this.triggerDisposables.forEach(dispose => dispose());\n    this.triggerDisposables.length = 0;\n  }\n  clearTogglingTimer() {\n    if (this.delayTimer) {\n      clearTimeout(this.delayTimer);\n      this.delayTimer = undefined;\n    }\n  }\n  static ɵfac = function NzTooltipBaseDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTooltipBaseDirective)(i0.ɵɵdirectiveInject(i0.Type));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzTooltipBaseDirective,\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTooltipBaseDirective, [{\n    type: Directive\n  }], () => [{\n    type: i0.Type\n  }], null);\n})();\nclass NzTooltipBaseComponent {\n  overlay;\n  noAnimation = inject(NzNoAnimationDirective, {\n    host: true,\n    optional: true\n  });\n  cdr = inject(ChangeDetectorRef);\n  directionality = inject(Directionality);\n  nzTitle = null;\n  nzContent = null;\n  nzArrowPointAtCenter = false;\n  nzOverlayClassName;\n  nzOverlayStyle = {};\n  nzOverlayClickable = true;\n  nzBackdrop = false;\n  nzMouseEnterDelay;\n  nzMouseLeaveDelay;\n  cdkConnectedOverlayPush = true;\n  nzVisibleChange = new Subject();\n  set nzVisible(value) {\n    const visible = toBoolean(value);\n    if (this._visible !== visible) {\n      this._visible = visible;\n      this.nzVisibleChange.next(visible);\n    }\n  }\n  get nzVisible() {\n    return this._visible;\n  }\n  _visible = false;\n  set nzTrigger(value) {\n    this._trigger = value;\n  }\n  get nzTrigger() {\n    return this._trigger;\n  }\n  _trigger = 'hover';\n  set nzPlacement(value) {\n    const preferredPosition = value.map(placement => POSITION_MAP[placement]);\n    this._positions = [...preferredPosition, ...DEFAULT_TOOLTIP_POSITIONS];\n  }\n  preferredPlacement = 'top';\n  origin;\n  dir = 'ltr';\n  _classMap = {};\n  _prefix = 'ant-tooltip';\n  _positions = [...DEFAULT_TOOLTIP_POSITIONS];\n  destroy$ = new Subject();\n  ngOnInit() {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n  }\n  ngOnDestroy() {\n    this.nzVisibleChange.complete();\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  show() {\n    if (this.nzVisible) {\n      return;\n    }\n    if (!this.isEmpty()) {\n      this.nzVisible = true;\n      this.nzVisibleChange.next(true);\n      this.cdr.detectChanges();\n    }\n    // for ltr for overlay to display tooltip in correct placement in rtl direction.\n    if (this.origin && this.overlay && this.overlay.overlayRef && this.overlay.overlayRef.getDirection() === 'rtl') {\n      this.overlay.overlayRef.setDirection('ltr');\n    }\n  }\n  hide() {\n    if (!this.nzVisible) {\n      return;\n    }\n    this.nzVisible = false;\n    this.nzVisibleChange.next(false);\n    this.cdr.detectChanges();\n  }\n  updateByDirective() {\n    this.updateStyles();\n    this.cdr.detectChanges();\n    Promise.resolve().then(() => {\n      this.updatePosition();\n      this.updateVisibilityByTitle();\n    });\n  }\n  /**\n   * Force the component to update its position.\n   */\n  updatePosition() {\n    if (this.origin && this.overlay && this.overlay.overlayRef) {\n      this.overlay.overlayRef.updatePosition();\n    }\n  }\n  onPositionChange(position) {\n    this.preferredPlacement = getPlacementName(position);\n    this.updateStyles();\n    // We have to trigger immediate change detection or the element would blink.\n    this.cdr.detectChanges();\n  }\n  setOverlayOrigin(origin) {\n    this.origin = origin;\n    this.cdr.markForCheck();\n  }\n  onClickOutside(event) {\n    if (!this.nzOverlayClickable) {\n      return;\n    }\n    const target = _getEventTarget(event);\n    if (!this.origin.nativeElement.contains(target) && this.nzTrigger !== null) {\n      this.hide();\n    }\n  }\n  /**\n   * Hide the component while the content is empty.\n   */\n  updateVisibilityByTitle() {\n    if (this.isEmpty()) {\n      this.hide();\n    }\n  }\n  updateStyles() {\n    this._classMap = {\n      ...this.transformClassListToMap(this.nzOverlayClassName),\n      [`${this._prefix}-placement-${this.preferredPlacement}`]: true\n    };\n  }\n  transformClassListToMap(klass) {\n    const result = {};\n    /**\n     * @see https://github.com/angular/angular/blob/f6e97763cfab9fa2bea6e6b1303b64f1b499c3ef/packages/common/src/directives/ng_class.ts#L92\n     */\n    const classes = klass !== null ? klass.split(/\\s+/) : [];\n    classes.forEach(className => result[className] = true);\n    return result;\n  }\n  static ɵfac = function NzTooltipBaseComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTooltipBaseComponent)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzTooltipBaseComponent,\n    viewQuery: function NzTooltipBaseComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.overlay = _t.first);\n      }\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTooltipBaseComponent, [{\n    type: Directive\n  }], null, {\n    overlay: [{\n      type: ViewChild,\n      args: ['overlay', {\n        static: false\n      }]\n    }]\n  });\n})();\nfunction isTooltipEmpty(value) {\n  return value instanceof TemplateRef ? false : value === '' || !isNotNil(value);\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTooltipDirective extends NzTooltipBaseDirective {\n  /* eslint-disable @angular-eslint/no-input-rename, @angular-eslint/no-output-rename */\n  title;\n  titleContext = null;\n  directiveTitle;\n  trigger = 'hover';\n  placement = 'top';\n  origin;\n  visible;\n  mouseEnterDelay;\n  mouseLeaveDelay;\n  overlayClassName;\n  overlayStyle;\n  arrowPointAtCenter;\n  cdkConnectedOverlayPush = true;\n  nzTooltipColor;\n  directiveContent = null;\n  content = null;\n  overlayClickable;\n  visibleChange = new EventEmitter();\n  constructor() {\n    super(NzToolTipComponent);\n  }\n  getProxyPropertyMap() {\n    return {\n      ...super.getProxyPropertyMap(),\n      nzTooltipColor: ['nzColor', () => this.nzTooltipColor],\n      titleContext: ['nzTitleContext', () => this.titleContext]\n    };\n  }\n  static ɵfac = function NzTooltipDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzTooltipDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NzTooltipDirective,\n    selectors: [[\"\", \"nz-tooltip\", \"\"]],\n    hostVars: 2,\n    hostBindings: function NzTooltipDirective_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-tooltip-open\", ctx.visible);\n      }\n    },\n    inputs: {\n      title: [0, \"nzTooltipTitle\", \"title\"],\n      titleContext: [0, \"nzTooltipTitleContext\", \"titleContext\"],\n      directiveTitle: [0, \"nz-tooltip\", \"directiveTitle\"],\n      trigger: [0, \"nzTooltipTrigger\", \"trigger\"],\n      placement: [0, \"nzTooltipPlacement\", \"placement\"],\n      origin: [0, \"nzTooltipOrigin\", \"origin\"],\n      visible: [0, \"nzTooltipVisible\", \"visible\"],\n      mouseEnterDelay: [0, \"nzTooltipMouseEnterDelay\", \"mouseEnterDelay\"],\n      mouseLeaveDelay: [0, \"nzTooltipMouseLeaveDelay\", \"mouseLeaveDelay\"],\n      overlayClassName: [0, \"nzTooltipOverlayClassName\", \"overlayClassName\"],\n      overlayStyle: [0, \"nzTooltipOverlayStyle\", \"overlayStyle\"],\n      arrowPointAtCenter: [2, \"nzTooltipArrowPointAtCenter\", \"arrowPointAtCenter\", booleanAttribute],\n      cdkConnectedOverlayPush: [2, \"cdkConnectedOverlayPush\", \"cdkConnectedOverlayPush\", booleanAttribute],\n      nzTooltipColor: \"nzTooltipColor\"\n    },\n    outputs: {\n      visibleChange: \"nzTooltipVisibleChange\"\n    },\n    exportAs: [\"nzTooltip\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTooltipDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-tooltip]',\n      exportAs: 'nzTooltip',\n      host: {\n        '[class.ant-tooltip-open]': 'visible'\n      }\n    }]\n  }], () => [], {\n    title: [{\n      type: Input,\n      args: ['nzTooltipTitle']\n    }],\n    titleContext: [{\n      type: Input,\n      args: ['nzTooltipTitleContext']\n    }],\n    directiveTitle: [{\n      type: Input,\n      args: ['nz-tooltip']\n    }],\n    trigger: [{\n      type: Input,\n      args: ['nzTooltipTrigger']\n    }],\n    placement: [{\n      type: Input,\n      args: ['nzTooltipPlacement']\n    }],\n    origin: [{\n      type: Input,\n      args: ['nzTooltipOrigin']\n    }],\n    visible: [{\n      type: Input,\n      args: ['nzTooltipVisible']\n    }],\n    mouseEnterDelay: [{\n      type: Input,\n      args: ['nzTooltipMouseEnterDelay']\n    }],\n    mouseLeaveDelay: [{\n      type: Input,\n      args: ['nzTooltipMouseLeaveDelay']\n    }],\n    overlayClassName: [{\n      type: Input,\n      args: ['nzTooltipOverlayClassName']\n    }],\n    overlayStyle: [{\n      type: Input,\n      args: ['nzTooltipOverlayStyle']\n    }],\n    arrowPointAtCenter: [{\n      type: Input,\n      args: [{\n        alias: 'nzTooltipArrowPointAtCenter',\n        transform: booleanAttribute\n      }]\n    }],\n    cdkConnectedOverlayPush: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzTooltipColor: [{\n      type: Input\n    }],\n    visibleChange: [{\n      type: Output,\n      args: ['nzTooltipVisibleChange']\n    }]\n  });\n})();\nclass NzToolTipComponent extends NzTooltipBaseComponent {\n  nzTitle = null;\n  nzTitleContext = null;\n  nzColor;\n  _contentStyleMap = {};\n  isEmpty() {\n    return isTooltipEmpty(this.nzTitle);\n  }\n  updateStyles() {\n    const isColorPreset = this.nzColor && isPresetColor(this.nzColor);\n    this._classMap = {\n      ...this.transformClassListToMap(this.nzOverlayClassName),\n      [`${this._prefix}-placement-${this.preferredPlacement}`]: true,\n      [`${this._prefix}-${this.nzColor}`]: isColorPreset\n    };\n    this._contentStyleMap = {\n      backgroundColor: !!this.nzColor && !isColorPreset ? this.nzColor : null,\n      '--antd-arrow-background-color': this.nzColor\n    };\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵNzToolTipComponent_BaseFactory;\n    return function NzToolTipComponent_Factory(__ngFactoryType__) {\n      return (ɵNzToolTipComponent_BaseFactory || (ɵNzToolTipComponent_BaseFactory = i0.ɵɵgetInheritedFactory(NzToolTipComponent)))(__ngFactoryType__ || NzToolTipComponent);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzToolTipComponent,\n    selectors: [[\"nz-tooltip\"]],\n    exportAs: [\"nzTooltipComponent\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 2,\n    vars: 5,\n    consts: [[\"overlay\", \"cdkConnectedOverlay\"], [\"cdkConnectedOverlay\", \"\", \"nzConnectedOverlay\", \"\", 3, \"overlayOutsideClick\", \"detach\", \"positionChange\", \"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayPush\", \"nzArrowPointAtCenter\"], [1, \"ant-tooltip\", 3, \"nzNoAnimation\"], [1, \"ant-tooltip-content\"], [1, \"ant-tooltip-arrow\"], [1, \"ant-tooltip-arrow-content\"], [1, \"ant-tooltip-inner\"], [4, \"nzStringTemplateOutlet\", \"nzStringTemplateOutletContext\"]],\n    template: function NzToolTipComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵtemplate(0, NzToolTipComponent_ng_template_0_Template, 6, 15, \"ng-template\", 1, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵlistener(\"overlayOutsideClick\", function NzToolTipComponent_Template_ng_template_overlayOutsideClick_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onClickOutside($event));\n        })(\"detach\", function NzToolTipComponent_Template_ng_template_detach_0_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.hide());\n        })(\"positionChange\", function NzToolTipComponent_Template_ng_template_positionChange_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onPositionChange($event));\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"cdkConnectedOverlayOrigin\", ctx.origin)(\"cdkConnectedOverlayOpen\", ctx._visible)(\"cdkConnectedOverlayPositions\", ctx._positions)(\"cdkConnectedOverlayPush\", ctx.cdkConnectedOverlayPush)(\"nzArrowPointAtCenter\", ctx.nzArrowPointAtCenter);\n      }\n    },\n    dependencies: [OverlayModule, i1.CdkConnectedOverlay, NzNoAnimationDirective, NzOutletModule, i2.NzStringTemplateOutletDirective, NzOverlayModule, i3.NzConnectedOverlayDirective],\n    encapsulation: 2,\n    data: {\n      animation: [zoomBigMotion]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzToolTipComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-tooltip',\n      exportAs: 'nzTooltipComponent',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      animations: [zoomBigMotion],\n      template: `\n    <ng-template\n      #overlay=\"cdkConnectedOverlay\"\n      cdkConnectedOverlay\n      nzConnectedOverlay\n      [cdkConnectedOverlayOrigin]=\"origin\"\n      [cdkConnectedOverlayOpen]=\"_visible\"\n      [cdkConnectedOverlayPositions]=\"_positions\"\n      [cdkConnectedOverlayPush]=\"cdkConnectedOverlayPush\"\n      [nzArrowPointAtCenter]=\"nzArrowPointAtCenter\"\n      (overlayOutsideClick)=\"onClickOutside($event)\"\n      (detach)=\"hide()\"\n      (positionChange)=\"onPositionChange($event)\"\n    >\n      <div\n        class=\"ant-tooltip\"\n        [class.ant-tooltip-rtl]=\"dir === 'rtl'\"\n        [class]=\"_classMap\"\n        [style]=\"nzOverlayStyle\"\n        [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n        [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n        [@zoomBigMotion]=\"'active'\"\n      >\n        <div class=\"ant-tooltip-content\">\n          <div class=\"ant-tooltip-arrow\">\n            <span class=\"ant-tooltip-arrow-content\" [style]=\"_contentStyleMap\"></span>\n          </div>\n          <div class=\"ant-tooltip-inner\" [style]=\"_contentStyleMap\">\n            <ng-container *nzStringTemplateOutlet=\"nzTitle; context: nzTitleContext\">{{ nzTitle }}</ng-container>\n          </div>\n        </div>\n      </div>\n    </ng-template>\n  `,\n      preserveWhitespaces: false,\n      imports: [OverlayModule, NzNoAnimationDirective, NzOutletModule, NzOverlayModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzToolTipModule {\n  static ɵfac = function NzToolTipModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzToolTipModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzToolTipModule,\n    imports: [NzToolTipComponent, NzTooltipDirective],\n    exports: [NzToolTipComponent, NzTooltipDirective]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [NzToolTipComponent]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzToolTipModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzToolTipComponent, NzTooltipDirective],\n      exports: [NzToolTipComponent, NzTooltipDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzToolTipComponent, NzToolTipModule, NzTooltipBaseComponent, NzTooltipBaseDirective, NzTooltipDirective, isTooltipEmpty };\n", "import * as i0 from '@angular/core';\nimport { ViewEncapsulation, ChangeDetectionStrategy, Component, booleanAttribute, Input, Directive, inject, ContentChild, NgModule } from '@angular/core';\nimport { NzGridModule } from 'ng-zorro-antd/grid';\nimport { AbstractControl, NgModel, FormControlName, FormControlDirective, NgControl } from '@angular/forms';\nimport { Subject, Subscription } from 'rxjs';\nimport { filter, map, takeUntil, startWith, tap } from 'rxjs/operators';\nimport { helpMotion } from 'ng-zorro-antd/core/animation';\nimport * as i2$1 from 'ng-zorro-antd/core/form';\nimport { NzFormStatusService } from 'ng-zorro-antd/core/form';\nimport * as i3 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { toBoolean } from 'ng-zorro-antd/core/util';\nimport { __esDecorate, __runInitializers } from 'tslib';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport * as i2 from '@angular/cdk/bidi';\nimport * as i1$1 from 'ng-zorro-antd/i18n';\nimport * as i2$2 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { NzTooltipDirective } from 'ng-zorro-antd/tooltip';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/** should add nz-row directive to host, track https://github.com/angular/angular/issues/8785 **/\nconst _c0 = [\"*\"];\nconst _c1 = a0 => [a0];\nconst _c2 = a0 => ({\n  $implicit: a0\n});\nfunction NzFormControlComponent_Conditional_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.innerTip);\n  }\n}\nfunction NzFormControlComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 4);\n    i0.ɵɵtemplate(2, NzFormControlComponent_Conditional_3_ng_container_2_Template, 2, 1, \"ng-container\", 5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@helpMotion\", undefined);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(i0.ɵɵpureFunction1(5, _c1, \"ant-form-item-explain-\" + ctx_r0.status));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.innerTip)(\"nzStringTemplateOutletContext\", i0.ɵɵpureFunction1(7, _c2, ctx_r0.validateControl));\n  }\n}\nfunction NzFormControlComponent_Conditional_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzExtra);\n  }\n}\nfunction NzFormControlComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, NzFormControlComponent_Conditional_4_ng_container_1_Template, 2, 1, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzExtra);\n  }\n}\nfunction NzFormLabelComponent_Conditional_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"nz-icon\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tooltipIconType_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzType\", tooltipIconType_r1)(\"nzTheme\", ctx_r1.tooltipIcon.theme);\n  }\n}\nfunction NzFormLabelComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 0);\n    i0.ɵɵtemplate(1, NzFormLabelComponent_Conditional_2_ng_container_1_Template, 2, 2, \"ng-container\", 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzTooltipTitle\", ctx_r1.nzTooltipTitle);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r1.tooltipIcon.type);\n  }\n}\nclass NzFormItemComponent {\n  cdr;\n  status = '';\n  hasFeedback = false;\n  withHelpClass = false;\n  destroy$ = new Subject();\n  setWithHelpViaTips(value) {\n    this.withHelpClass = value;\n    this.cdr.markForCheck();\n  }\n  setStatus(status) {\n    this.status = status;\n    this.cdr.markForCheck();\n  }\n  setHasFeedback(hasFeedback) {\n    this.hasFeedback = hasFeedback;\n    this.cdr.markForCheck();\n  }\n  constructor(cdr) {\n    this.cdr = cdr;\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static ɵfac = function NzFormItemComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzFormItemComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzFormItemComponent,\n    selectors: [[\"nz-form-item\"]],\n    hostAttrs: [1, \"ant-form-item\"],\n    hostVars: 12,\n    hostBindings: function NzFormItemComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-form-item-has-success\", ctx.status === \"success\")(\"ant-form-item-has-warning\", ctx.status === \"warning\")(\"ant-form-item-has-error\", ctx.status === \"error\")(\"ant-form-item-is-validating\", ctx.status === \"validating\")(\"ant-form-item-has-feedback\", ctx.hasFeedback && ctx.status)(\"ant-form-item-with-help\", ctx.withHelpClass);\n      }\n    },\n    exportAs: [\"nzFormItem\"],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function NzFormItemComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormItemComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-form-item',\n      exportAs: 'nzFormItem',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'ant-form-item',\n        '[class.ant-form-item-has-success]': 'status === \"success\"',\n        '[class.ant-form-item-has-warning]': 'status === \"warning\"',\n        '[class.ant-form-item-has-error]': 'status === \"error\"',\n        '[class.ant-form-item-is-validating]': 'status === \"validating\"',\n        '[class.ant-form-item-has-feedback]': 'hasFeedback && status',\n        '[class.ant-form-item-with-help]': 'withHelpClass'\n      },\n      template: `<ng-content></ng-content>`\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], null);\n})();\nconst NZ_CONFIG_MODULE_NAME = 'form';\nconst DefaultTooltipIcon = {\n  type: 'question-circle',\n  theme: 'outline'\n};\nlet NzFormDirective = (() => {\n  let _nzNoColon_decorators;\n  let _nzNoColon_initializers = [];\n  let _nzNoColon_extraInitializers = [];\n  let _nzAutoTips_decorators;\n  let _nzAutoTips_initializers = [];\n  let _nzAutoTips_extraInitializers = [];\n  let _nzTooltipIcon_decorators;\n  let _nzTooltipIcon_initializers = [];\n  let _nzTooltipIcon_extraInitializers = [];\n  let _nzLabelWrap_decorators;\n  let _nzLabelWrap_initializers = [];\n  let _nzLabelWrap_extraInitializers = [];\n  return class NzFormDirective {\n    static {\n      const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(null) : void 0;\n      _nzNoColon_decorators = [WithConfig()];\n      _nzAutoTips_decorators = [WithConfig()];\n      _nzTooltipIcon_decorators = [WithConfig()];\n      _nzLabelWrap_decorators = [WithConfig()];\n      __esDecorate(null, null, _nzNoColon_decorators, {\n        kind: \"field\",\n        name: \"nzNoColon\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzNoColon\" in obj,\n          get: obj => obj.nzNoColon,\n          set: (obj, value) => {\n            obj.nzNoColon = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzNoColon_initializers, _nzNoColon_extraInitializers);\n      __esDecorate(null, null, _nzAutoTips_decorators, {\n        kind: \"field\",\n        name: \"nzAutoTips\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzAutoTips\" in obj,\n          get: obj => obj.nzAutoTips,\n          set: (obj, value) => {\n            obj.nzAutoTips = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzAutoTips_initializers, _nzAutoTips_extraInitializers);\n      __esDecorate(null, null, _nzTooltipIcon_decorators, {\n        kind: \"field\",\n        name: \"nzTooltipIcon\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzTooltipIcon\" in obj,\n          get: obj => obj.nzTooltipIcon,\n          set: (obj, value) => {\n            obj.nzTooltipIcon = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzTooltipIcon_initializers, _nzTooltipIcon_extraInitializers);\n      __esDecorate(null, null, _nzLabelWrap_decorators, {\n        kind: \"field\",\n        name: \"nzLabelWrap\",\n        static: false,\n        private: false,\n        access: {\n          has: obj => \"nzLabelWrap\" in obj,\n          get: obj => obj.nzLabelWrap,\n          set: (obj, value) => {\n            obj.nzLabelWrap = value;\n          }\n        },\n        metadata: _metadata\n      }, _nzLabelWrap_initializers, _nzLabelWrap_extraInitializers);\n      if (_metadata) Object.defineProperty(this, Symbol.metadata, {\n        enumerable: true,\n        configurable: true,\n        writable: true,\n        value: _metadata\n      });\n    }\n    nzConfigService;\n    directionality;\n    _nzModuleName = NZ_CONFIG_MODULE_NAME;\n    nzLayout = 'horizontal';\n    nzNoColon = __runInitializers(this, _nzNoColon_initializers, false);\n    nzAutoTips = (__runInitializers(this, _nzNoColon_extraInitializers), __runInitializers(this, _nzAutoTips_initializers, {}));\n    nzDisableAutoTips = (__runInitializers(this, _nzAutoTips_extraInitializers), false);\n    nzTooltipIcon = __runInitializers(this, _nzTooltipIcon_initializers, DefaultTooltipIcon);\n    nzLabelAlign = (__runInitializers(this, _nzTooltipIcon_extraInitializers), 'right');\n    nzLabelWrap = __runInitializers(this, _nzLabelWrap_initializers, false);\n    dir = (__runInitializers(this, _nzLabelWrap_extraInitializers), 'ltr');\n    destroy$ = new Subject();\n    inputChanges$ = new Subject();\n    getInputObservable(changeType) {\n      return this.inputChanges$.pipe(filter(changes => changeType in changes), map(value => value[changeType]));\n    }\n    constructor(nzConfigService, directionality) {\n      this.nzConfigService = nzConfigService;\n      this.directionality = directionality;\n      this.dir = this.directionality.value;\n      this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n        this.dir = direction;\n      });\n    }\n    ngOnChanges(changes) {\n      this.inputChanges$.next(changes);\n    }\n    ngOnDestroy() {\n      this.inputChanges$.complete();\n      this.destroy$.next(true);\n      this.destroy$.complete();\n    }\n    static ɵfac = function NzFormDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NzFormDirective)(i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i2.Directionality));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzFormDirective,\n      selectors: [[\"\", \"nz-form\", \"\"]],\n      hostAttrs: [1, \"ant-form\"],\n      hostVars: 8,\n      hostBindings: function NzFormDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-form-horizontal\", ctx.nzLayout === \"horizontal\")(\"ant-form-vertical\", ctx.nzLayout === \"vertical\")(\"ant-form-inline\", ctx.nzLayout === \"inline\")(\"ant-form-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        nzLayout: \"nzLayout\",\n        nzNoColon: [2, \"nzNoColon\", \"nzNoColon\", booleanAttribute],\n        nzAutoTips: \"nzAutoTips\",\n        nzDisableAutoTips: [2, \"nzDisableAutoTips\", \"nzDisableAutoTips\", booleanAttribute],\n        nzTooltipIcon: \"nzTooltipIcon\",\n        nzLabelAlign: \"nzLabelAlign\",\n        nzLabelWrap: [2, \"nzLabelWrap\", \"nzLabelWrap\", booleanAttribute]\n      },\n      exportAs: [\"nzForm\"],\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  };\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-form]',\n      exportAs: 'nzForm',\n      host: {\n        class: 'ant-form',\n        '[class.ant-form-horizontal]': `nzLayout === 'horizontal'`,\n        '[class.ant-form-vertical]': `nzLayout === 'vertical'`,\n        '[class.ant-form-inline]': `nzLayout === 'inline'`,\n        '[class.ant-form-rtl]': `dir === 'rtl'`\n      }\n    }]\n  }], () => [{\n    type: i1.NzConfigService\n  }, {\n    type: i2.Directionality\n  }], {\n    nzLayout: [{\n      type: Input\n    }],\n    nzNoColon: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzAutoTips: [{\n      type: Input\n    }],\n    nzDisableAutoTips: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzTooltipIcon: [{\n      type: Input\n    }],\n    nzLabelAlign: [{\n      type: Input\n    }],\n    nzLabelWrap: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzFormControlComponent {\n  cdr;\n  nzFormStatusService;\n  _hasFeedback = false;\n  validateChanges = Subscription.EMPTY;\n  validateString = null;\n  destroyed$ = new Subject();\n  localeId;\n  autoErrorTip;\n  get disableAutoTips() {\n    return this.nzDisableAutoTips !== undefined ? toBoolean(this.nzDisableAutoTips) : !!this.nzFormDirective?.nzDisableAutoTips;\n  }\n  status = '';\n  validateControl = null;\n  innerTip = null;\n  defaultValidateControl;\n  nzSuccessTip;\n  nzWarningTip;\n  nzErrorTip;\n  nzValidatingTip;\n  nzExtra;\n  nzAutoTips = {};\n  nzDisableAutoTips;\n  set nzHasFeedback(value) {\n    this._hasFeedback = value;\n    this.nzFormStatusService.formStatusChanges.next({\n      status: this.status,\n      hasFeedback: this._hasFeedback\n    });\n    if (this.nzFormItemComponent) {\n      this.nzFormItemComponent.setHasFeedback(this._hasFeedback);\n    }\n  }\n  get nzHasFeedback() {\n    return this._hasFeedback;\n  }\n  set nzValidateStatus(value) {\n    if (value instanceof AbstractControl || value instanceof NgModel) {\n      this.validateControl = value;\n      this.validateString = null;\n      this.watchControl();\n    } else if (value instanceof FormControlName) {\n      this.validateControl = value.control;\n      this.validateString = null;\n      this.watchControl();\n    } else {\n      this.validateString = value;\n      this.validateControl = null;\n      this.setStatus();\n    }\n  }\n  watchControl() {\n    this.validateChanges.unsubscribe();\n    /** miss detect https://github.com/angular/angular/issues/10887 **/\n    if (this.validateControl && this.validateControl.statusChanges) {\n      this.validateChanges = this.validateControl.statusChanges.pipe(startWith(null), takeUntil(this.destroyed$)).subscribe(() => {\n        if (!this.disableAutoTips) {\n          this.updateAutoErrorTip();\n        }\n        this.setStatus();\n        this.cdr.markForCheck();\n      });\n    }\n  }\n  setStatus() {\n    this.status = this.getControlStatus(this.validateString);\n    this.innerTip = this.getInnerTip(this.status);\n    this.nzFormStatusService.formStatusChanges.next({\n      status: this.status,\n      hasFeedback: this.nzHasFeedback\n    });\n    if (this.nzFormItemComponent) {\n      this.nzFormItemComponent.setWithHelpViaTips(!!this.innerTip);\n      this.nzFormItemComponent.setStatus(this.status);\n    }\n  }\n  getControlStatus(validateString) {\n    let status;\n    if (validateString === 'warning' || this.validateControlStatus('INVALID', 'warning')) {\n      status = 'warning';\n    } else if (validateString === 'error' || this.validateControlStatus('INVALID')) {\n      status = 'error';\n    } else if (validateString === 'validating' || validateString === 'pending' || this.validateControlStatus('PENDING')) {\n      status = 'validating';\n    } else if (validateString === 'success' || this.validateControlStatus('VALID')) {\n      status = 'success';\n    } else {\n      status = '';\n    }\n    return status;\n  }\n  validateControlStatus(validStatus, statusType) {\n    if (!this.validateControl) {\n      return false;\n    } else {\n      const {\n        dirty,\n        touched,\n        status\n      } = this.validateControl;\n      return (!!dirty || !!touched) && (statusType ? this.validateControl.hasError(statusType) : status === validStatus);\n    }\n  }\n  getInnerTip(status) {\n    switch (status) {\n      case 'error':\n        return !this.disableAutoTips && this.autoErrorTip || this.nzErrorTip || null;\n      case 'validating':\n        return this.nzValidatingTip || null;\n      case 'success':\n        return this.nzSuccessTip || null;\n      case 'warning':\n        return this.nzWarningTip || null;\n      default:\n        return null;\n    }\n  }\n  updateAutoErrorTip() {\n    if (this.validateControl) {\n      const errors = this.validateControl.errors || {};\n      let autoErrorTip = '';\n      for (const key in errors) {\n        if (errors.hasOwnProperty(key)) {\n          autoErrorTip = errors[key]?.[this.localeId] ?? this.nzAutoTips?.[this.localeId]?.[key] ?? this.nzAutoTips.default?.[key] ?? this.nzFormDirective?.nzAutoTips?.[this.localeId]?.[key] ?? this.nzFormDirective?.nzAutoTips.default?.[key];\n        }\n        if (autoErrorTip) {\n          break;\n        }\n      }\n      this.autoErrorTip = autoErrorTip;\n    }\n  }\n  subscribeAutoTips(observable) {\n    observable?.pipe(takeUntil(this.destroyed$)).subscribe(() => {\n      if (!this.disableAutoTips) {\n        this.updateAutoErrorTip();\n        this.setStatus();\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  nzFormItemComponent = inject(NzFormItemComponent, {\n    host: true,\n    optional: true\n  });\n  nzFormDirective = inject(NzFormDirective, {\n    optional: true\n  });\n  constructor(cdr, i18n, nzFormStatusService) {\n    this.cdr = cdr;\n    this.nzFormStatusService = nzFormStatusService;\n    this.subscribeAutoTips(i18n.localeChange.pipe(tap(locale => this.localeId = locale.locale)));\n    this.subscribeAutoTips(this.nzFormDirective?.getInputObservable('nzAutoTips'));\n    this.subscribeAutoTips(this.nzFormDirective?.getInputObservable('nzDisableAutoTips').pipe(filter(() => this.nzDisableAutoTips === undefined)));\n  }\n  ngOnChanges(changes) {\n    const {\n      nzDisableAutoTips,\n      nzAutoTips,\n      nzSuccessTip,\n      nzWarningTip,\n      nzErrorTip,\n      nzValidatingTip\n    } = changes;\n    if (nzDisableAutoTips || nzAutoTips) {\n      this.updateAutoErrorTip();\n      this.setStatus();\n    } else if (nzSuccessTip || nzWarningTip || nzErrorTip || nzValidatingTip) {\n      this.setStatus();\n    }\n  }\n  ngOnInit() {\n    this.setStatus();\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  ngAfterContentInit() {\n    if (!this.validateControl && !this.validateString) {\n      if (this.defaultValidateControl instanceof FormControlDirective) {\n        this.nzValidateStatus = this.defaultValidateControl.control;\n      } else {\n        this.nzValidateStatus = this.defaultValidateControl;\n      }\n    }\n  }\n  static ɵfac = function NzFormControlComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzFormControlComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.NzI18nService), i0.ɵɵdirectiveInject(i2$1.NzFormStatusService));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzFormControlComponent,\n    selectors: [[\"nz-form-control\"]],\n    contentQueries: function NzFormControlComponent_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, NgControl, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.defaultValidateControl = _t.first);\n      }\n    },\n    hostAttrs: [1, \"ant-form-item-control\"],\n    inputs: {\n      nzSuccessTip: \"nzSuccessTip\",\n      nzWarningTip: \"nzWarningTip\",\n      nzErrorTip: \"nzErrorTip\",\n      nzValidatingTip: \"nzValidatingTip\",\n      nzExtra: \"nzExtra\",\n      nzAutoTips: \"nzAutoTips\",\n      nzDisableAutoTips: [2, \"nzDisableAutoTips\", \"nzDisableAutoTips\", booleanAttribute],\n      nzHasFeedback: [2, \"nzHasFeedback\", \"nzHasFeedback\", booleanAttribute],\n      nzValidateStatus: \"nzValidateStatus\"\n    },\n    exportAs: [\"nzFormControl\"],\n    features: [i0.ɵɵProvidersFeature([NzFormStatusService]), i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c0,\n    decls: 5,\n    vars: 2,\n    consts: [[1, \"ant-form-item-control-input\"], [1, \"ant-form-item-control-input-content\"], [1, \"ant-form-item-explain\", \"ant-form-item-explain-connected\"], [1, \"ant-form-item-extra\"], [\"role\", \"alert\"], [4, \"nzStringTemplateOutlet\", \"nzStringTemplateOutletContext\"], [4, \"nzStringTemplateOutlet\"]],\n    template: function NzFormControlComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵprojection(2);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(3, NzFormControlComponent_Conditional_3_Template, 3, 9, \"div\", 2)(4, NzFormControlComponent_Conditional_4_Template, 2, 1, \"div\", 3);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵconditional(ctx.innerTip ? 3 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.nzExtra ? 4 : -1);\n      }\n    },\n    dependencies: [NzOutletModule, i3.NzStringTemplateOutletDirective],\n    encapsulation: 2,\n    data: {\n      animation: [helpMotion]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormControlComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-form-control',\n      exportAs: 'nzFormControl',\n      preserveWhitespaces: false,\n      animations: [helpMotion],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <div class=\"ant-form-item-control-input\">\n      <div class=\"ant-form-item-control-input-content\">\n        <ng-content></ng-content>\n      </div>\n    </div>\n    @if (innerTip) {\n      <div @helpMotion class=\"ant-form-item-explain ant-form-item-explain-connected\">\n        <div role=\"alert\" [class]=\"['ant-form-item-explain-' + status]\">\n          <ng-container *nzStringTemplateOutlet=\"innerTip; context: { $implicit: validateControl }\">{{\n            innerTip\n          }}</ng-container>\n        </div>\n      </div>\n    }\n\n    @if (nzExtra) {\n      <div class=\"ant-form-item-extra\">\n        <ng-container *nzStringTemplateOutlet=\"nzExtra\">{{ nzExtra }}</ng-container>\n      </div>\n    }\n  `,\n      providers: [NzFormStatusService],\n      host: {\n        class: 'ant-form-item-control'\n      },\n      imports: [NzOutletModule]\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1$1.NzI18nService\n  }, {\n    type: i2$1.NzFormStatusService\n  }], {\n    defaultValidateControl: [{\n      type: ContentChild,\n      args: [NgControl, {\n        static: false\n      }]\n    }],\n    nzSuccessTip: [{\n      type: Input\n    }],\n    nzWarningTip: [{\n      type: Input\n    }],\n    nzErrorTip: [{\n      type: Input\n    }],\n    nzValidatingTip: [{\n      type: Input\n    }],\n    nzExtra: [{\n      type: Input\n    }],\n    nzAutoTips: [{\n      type: Input\n    }],\n    nzDisableAutoTips: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzHasFeedback: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzValidateStatus: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction toTooltipIcon(value) {\n  const icon = typeof value === 'string' ? {\n    type: value\n  } : value;\n  return {\n    ...DefaultTooltipIcon,\n    ...icon\n  };\n}\nclass NzFormLabelComponent {\n  cdr;\n  nzFor;\n  nzRequired = false;\n  set nzNoColon(value) {\n    this.noColon = value;\n  }\n  get nzNoColon() {\n    return this.noColon !== 'default' ? this.noColon : !!this.nzFormDirective?.nzNoColon;\n  }\n  noColon = 'default';\n  nzTooltipTitle;\n  set nzTooltipIcon(value) {\n    this._tooltipIcon = toTooltipIcon(value);\n  }\n  // due to 'get' and 'set' accessor must have the same type, so it was renamed to `tooltipIcon`\n  get tooltipIcon() {\n    return this._tooltipIcon !== 'default' ? this._tooltipIcon : toTooltipIcon(this.nzFormDirective?.nzTooltipIcon || DefaultTooltipIcon);\n  }\n  _tooltipIcon = 'default';\n  set nzLabelAlign(value) {\n    this.labelAlign = value;\n  }\n  get nzLabelAlign() {\n    return this.labelAlign !== 'default' ? this.labelAlign : this.nzFormDirective?.nzLabelAlign || 'right';\n  }\n  labelAlign = 'default';\n  set nzLabelWrap(value) {\n    this.labelWrap = value;\n  }\n  get nzLabelWrap() {\n    return this.labelWrap !== 'default' ? this.labelWrap : !!this.nzFormDirective?.nzLabelWrap;\n  }\n  labelWrap = 'default';\n  destroy$ = new Subject();\n  nzFormDirective = inject(NzFormDirective, {\n    skipSelf: true,\n    optional: true\n  });\n  constructor(cdr) {\n    this.cdr = cdr;\n    if (this.nzFormDirective) {\n      this.nzFormDirective.getInputObservable('nzNoColon').pipe(filter(() => this.noColon === 'default'), takeUntil(this.destroy$)).subscribe(() => this.cdr.markForCheck());\n      this.nzFormDirective.getInputObservable('nzTooltipIcon').pipe(filter(() => this._tooltipIcon === 'default'), takeUntil(this.destroy$)).subscribe(() => this.cdr.markForCheck());\n      this.nzFormDirective.getInputObservable('nzLabelAlign').pipe(filter(() => this.labelAlign === 'default'), takeUntil(this.destroy$)).subscribe(() => this.cdr.markForCheck());\n      this.nzFormDirective.getInputObservable('nzLabelWrap').pipe(filter(() => this.labelWrap === 'default'), takeUntil(this.destroy$)).subscribe(() => this.cdr.markForCheck());\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static ɵfac = function NzFormLabelComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzFormLabelComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzFormLabelComponent,\n    selectors: [[\"nz-form-label\"]],\n    hostAttrs: [1, \"ant-form-item-label\"],\n    hostVars: 4,\n    hostBindings: function NzFormLabelComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-form-item-label-left\", ctx.nzLabelAlign === \"left\")(\"ant-form-item-label-wrap\", ctx.nzLabelWrap);\n      }\n    },\n    inputs: {\n      nzFor: \"nzFor\",\n      nzRequired: [2, \"nzRequired\", \"nzRequired\", booleanAttribute],\n      nzNoColon: [2, \"nzNoColon\", \"nzNoColon\", booleanAttribute],\n      nzTooltipTitle: \"nzTooltipTitle\",\n      nzTooltipIcon: \"nzTooltipIcon\",\n      nzLabelAlign: \"nzLabelAlign\",\n      nzLabelWrap: [2, \"nzLabelWrap\", \"nzLabelWrap\", booleanAttribute]\n    },\n    exportAs: [\"nzFormLabel\"],\n    ngContentSelectors: _c0,\n    decls: 3,\n    vars: 6,\n    consts: [[\"nz-tooltip\", \"\", 1, \"ant-form-item-tooltip\", 3, \"nzTooltipTitle\"], [4, \"nzStringTemplateOutlet\"], [3, \"nzType\", \"nzTheme\"]],\n    template: function NzFormLabelComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"label\");\n        i0.ɵɵprojection(1);\n        i0.ɵɵtemplate(2, NzFormLabelComponent_Conditional_2_Template, 2, 2, \"span\", 0);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ant-form-item-no-colon\", ctx.nzNoColon)(\"ant-form-item-required\", ctx.nzRequired);\n        i0.ɵɵattribute(\"for\", ctx.nzFor);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(ctx.nzTooltipTitle ? 2 : -1);\n      }\n    },\n    dependencies: [NzOutletModule, i3.NzStringTemplateOutletDirective, NzTooltipDirective, NzIconModule, i2$2.NzIconDirective],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormLabelComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-form-label',\n      exportAs: 'nzFormLabel',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <label [attr.for]=\"nzFor\" [class.ant-form-item-no-colon]=\"nzNoColon\" [class.ant-form-item-required]=\"nzRequired\">\n      <ng-content></ng-content>\n      @if (nzTooltipTitle) {\n        <span class=\"ant-form-item-tooltip\" nz-tooltip [nzTooltipTitle]=\"nzTooltipTitle\">\n          <ng-container *nzStringTemplateOutlet=\"tooltipIcon.type; let tooltipIconType\">\n            <nz-icon [nzType]=\"tooltipIconType\" [nzTheme]=\"tooltipIcon.theme\" />\n          </ng-container>\n        </span>\n      }\n    </label>\n  `,\n      host: {\n        class: 'ant-form-item-label',\n        '[class.ant-form-item-label-left]': `nzLabelAlign === 'left'`,\n        '[class.ant-form-item-label-wrap]': `nzLabelWrap`\n      },\n      imports: [NzOutletModule, NzTooltipDirective, NzIconModule]\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], {\n    nzFor: [{\n      type: Input\n    }],\n    nzRequired: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzNoColon: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    nzTooltipTitle: [{\n      type: Input\n    }],\n    nzTooltipIcon: [{\n      type: Input\n    }],\n    nzLabelAlign: [{\n      type: Input\n    }],\n    nzLabelWrap: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzFormSplitComponent {\n  static ɵfac = function NzFormSplitComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzFormSplitComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzFormSplitComponent,\n    selectors: [[\"nz-form-split\"]],\n    hostAttrs: [1, \"ant-form-split\"],\n    exportAs: [\"nzFormSplit\"],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function NzFormSplitComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormSplitComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-form-split',\n      exportAs: 'nzFormSplit',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: ` <ng-content></ng-content> `,\n      host: {\n        class: 'ant-form-split'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzFormTextComponent {\n  static ɵfac = function NzFormTextComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzFormTextComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NzFormTextComponent,\n    selectors: [[\"nz-form-text\"]],\n    hostAttrs: [1, \"ant-form-text\"],\n    exportAs: [\"nzFormText\"],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function NzFormTextComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormTextComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-form-text',\n      exportAs: 'nzFormText',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: ` <ng-content></ng-content> `,\n      host: {\n        class: 'ant-form-text'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzFormModule {\n  static ɵfac = function NzFormModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NzFormModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NzFormModule,\n    imports: [NzFormDirective, NzFormItemComponent, NzFormLabelComponent, NzFormControlComponent, NzFormTextComponent, NzFormSplitComponent],\n    exports: [NzGridModule, NzFormDirective, NzFormItemComponent, NzFormLabelComponent, NzFormControlComponent, NzFormTextComponent, NzFormSplitComponent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [NzFormLabelComponent, NzFormControlComponent, NzGridModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzFormDirective, NzFormItemComponent, NzFormLabelComponent, NzFormControlComponent, NzFormTextComponent, NzFormSplitComponent],\n      exports: [NzGridModule, NzFormDirective, NzFormItemComponent, NzFormLabelComponent, NzFormControlComponent, NzFormTextComponent, NzFormSplitComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DefaultTooltipIcon, NzFormControlComponent, NzFormDirective, NzFormItemComponent, NzFormLabelComponent, NzFormModule, NzFormSplitComponent, NzFormTextComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,IAAM,MAAM,CAAC,SAAS;AACtB,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,OAAO;AAAA,EACrC;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AACvD,IAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,gBAAgB,CAAC;AAClG,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,cAAc;AACnC,IAAG,WAAW,OAAO,SAAS;AAC9B,IAAG,YAAY,mBAAmB,OAAO,QAAQ,KAAK;AACtD,IAAG,WAAW,cAAc,CAAC,EAAE,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,cAAc,EAAE,iBAAiB,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,aAAa,EAAE,kBAAkB,QAAQ;AACvN,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,OAAO,gBAAgB;AACrC,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,gBAAgB;AACrC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,OAAO,EAAE,iCAAiC,OAAO,cAAc;AAAA,EAChH;AACF;AACA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA,gBAAgB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA,EAIjC,IAAI,SAAS;AACX,WAAO,KAAK,SAAS,KAAK,kBAAkB;AAAA,EAC9C;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,WAAW,KAAK,oBAAoB;AAAA,EAClD;AAAA,EACA,IAAI,WAAW;AACb,WAAO,OAAO,KAAK,YAAY,cAAc,KAAK,UAAU;AAAA,EAC9D;AAAA,EACA,IAAI,aAAa;AACf,UAAM,IAAI,KAAK;AACf,WAAO,MAAM,QAAQ,CAAC,KAAK,EAAE,SAAS,IAAI,IAAI,OAAO,MAAM,YAAY,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK;AAAA,EACzF;AAAA,EACA,IAAI,WAAW;AACb,YAAQ,OAAO,KAAK,YAAY,cAAc,KAAK,UAAU,KAAK,oBAAoB;AAAA,EACxF;AAAA,EACA,IAAI,mBAAmB;AACrB,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,IAAI,mBAAmB;AACrB,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,KAAK,oBAAoB;AAAA,EAClC;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,KAAK,oBAAoB;AAAA,EAClC;AAAA,EACA,kBAAkB;AAAA,EAClB,sBAAsB;AACpB,WAAO;AAAA,MACL,aAAa,CAAC,eAAe,MAAM,CAAC,CAAC,KAAK,WAAW;AAAA,IACvD;AAAA,EACF;AAAA,EACA;AAAA,EACA,WAAW,IAAI,QAAQ;AAAA,EACvB,qBAAqB,CAAC;AAAA,EACtB;AAAA,EACA,aAAa,OAAO,UAAU;AAAA,EAC9B,WAAW,OAAO,gBAAgB;AAAA,EAClC,WAAW,OAAO,SAAS;AAAA,EAC3B,cAAc,OAAO,wBAAwB;AAAA,IAC3C,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,kBAAkB,OAAO,eAAe;AAAA,EACxC,aAAa,OAAO,WAAW;AAAA,EAC/B,YAAY,eAAe;AACzB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,kBAAkB;AAChB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,WAAK,gBAAgB;AACrB,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,WAAW,CAAC,QAAQ,cAAc,GAAG;AACvC,WAAK,iBAAiB;AAAA,IACxB;AACA,QAAI,KAAK,WAAW;AAClB,WAAK,0BAA0B,OAAO;AAAA,IACxC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAEvB,SAAK,mBAAmB;AACxB,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,OAAO;AACL,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA,EACA,OAAO;AACL,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,eAAe;AAAA,IAChC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB;AAChB,UAAM,eAAe,KAAK,SAAS,gBAAgB,KAAK,aAAa;AACrE,SAAK,YAAY,aAAa;AAE9B,SAAK,SAAS,YAAY,KAAK,SAAS,WAAW,KAAK,WAAW,aAAa,GAAG,aAAa,SAAS,aAAa;AACtH,SAAK,UAAU,iBAAiB,KAAK,UAAU,KAAK,UAAU;AAC9D,SAAK,eAAe;AACpB,UAAM,mBAAmB,KAAK,UAAU,gBAAgB,KAAK,qBAAqB,CAAC;AACnF,qBAAiB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,aAAW;AACnE,WAAK,kBAAkB;AACvB,WAAK,cAAc,KAAK,OAAO;AAAA,IACjC,CAAC;AAID,qBAAiB,KAAK,OAAO,aAAW,OAAO,GAAG,MAAM,GAAG,aAAa,GAAG,OAAO,MAAM,QAAQ,KAAK,WAAW,SAAS,UAAU,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC/K,WAAK,WAAW,eAAe;AAAA,IACjC,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB;AAGjB,UAAM,KAAK,KAAK,WAAW;AAC3B,UAAM,UAAU,KAAK;AACrB,SAAK,uBAAuB;AAC5B,QAAI,YAAY,SAAS;AACvB,UAAI;AACJ,WAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,IAAI,cAAc,MAAM;AACxE,aAAK,gBAAgB,MAAM,MAAM,KAAK,gBAAgB;AAAA,MACxD,CAAC,CAAC;AACF,WAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,IAAI,cAAc,MAAM;AACxE,aAAK,gBAAgB,MAAM,OAAO,KAAK,gBAAgB;AACvD,YAAI,KAAK,WAAW,QAAQ,cAAc,CAAC,gBAAgB;AACzD,2BAAiB,KAAK,UAAU,QAAQ,WAAW;AACnD,eAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,gBAAgB,cAAc,MAAM;AACpF,iBAAK,gBAAgB,OAAO,MAAM,KAAK,gBAAgB;AAAA,UACzD,CAAC,CAAC;AACF,eAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,gBAAgB,cAAc,MAAM;AACpF,iBAAK,gBAAgB,OAAO,OAAO,KAAK,gBAAgB;AAAA,UAC1D,CAAC,CAAC;AAAA,QACJ;AAAA,MACF,CAAC,CAAC;AAAA,IACJ,WAAW,YAAY,SAAS;AAC9B,WAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,IAAI,WAAW,MAAM,KAAK,KAAK,CAAC,CAAC;AACnF,WAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,IAAI,YAAY,MAAM,KAAK,KAAK,CAAC,CAAC;AAAA,IACtF,WAAW,YAAY,SAAS;AAC9B,WAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,IAAI,SAAS,OAAK;AAClE,UAAE,eAAe;AACjB,aAAK,KAAK;AAAA,MACZ,CAAC,CAAC;AAAA,IACJ;AAAA,EAEF;AAAA,EACA,0BAA0B,SAAS;AACjC,SAAK,uBAAuB,OAAO,KAAK,OAAO,CAAC;AAAA,EAClD;AAAA,EACA,uBAAuB,MAAM;AAC3B,UAAM,oBAAoB;AAAA;AAAA,MAExB,OAAO,CAAC,WAAW,MAAM,KAAK,MAAM;AAAA,MACpC,gBAAgB,CAAC,WAAW,MAAM,KAAK,MAAM;AAAA,MAC7C,SAAS,CAAC,aAAa,MAAM,KAAK,QAAQ;AAAA,MAC1C,kBAAkB,CAAC,aAAa,MAAM,KAAK,QAAQ;AAAA,MACnD,SAAS,CAAC,aAAa,MAAM,KAAK,QAAQ;AAAA,MAC1C,WAAW,CAAC,eAAe,MAAM,KAAK,UAAU;AAAA,MAChD,SAAS,CAAC,aAAa,MAAM,KAAK,QAAQ;AAAA,MAC1C,iBAAiB,CAAC,qBAAqB,MAAM,KAAK,gBAAgB;AAAA,MAClE,iBAAiB,CAAC,qBAAqB,MAAM,KAAK,gBAAgB;AAAA,MAClE,kBAAkB,CAAC,sBAAsB,MAAM,KAAK,iBAAiB;AAAA,MACrE,cAAc,CAAC,kBAAkB,MAAM,KAAK,aAAa;AAAA,MACzD,kBAAkB,CAAC,sBAAsB,MAAM,KAAK,iBAAiB;AAAA,MACrE,oBAAoB,CAAC,wBAAwB,MAAM,KAAK,kBAAkB;AAAA,MAC1E,yBAAyB,CAAC,2BAA2B,MAAM,KAAK,uBAAuB;AAAA,OACpF,KAAK,oBAAoB;AAE9B,KAAC,QAAQ,OAAO,KAAK,iBAAiB,EAAE,OAAO,SAAO,CAAC,IAAI,WAAW,WAAW,CAAC,GAAG,QAAQ,cAAY;AACvG,UAAI,kBAAkB,QAAQ,GAAG;AAC/B,cAAM,CAAC,MAAM,OAAO,IAAI,kBAAkB,QAAQ;AAClD,aAAK,qBAAqB,MAAM,QAAQ,CAAC;AAAA,MAC3C;AAAA,IACF,CAAC;AACD,SAAK,WAAW,kBAAkB;AAAA,EACpC;AAAA,EACA,iBAAiB;AACf,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,qBAAqB,KAAK,OAAO;AAC/B,QAAI,OAAO,UAAU,aAAa;AAEhC,WAAK,UAAU,GAAG,IAAI;AAAA,IACxB;AAAA,EACF;AAAA,EACA,gBAAgB,UAAU,SAASA,SAAQ,IAAI;AAC7C,QAAI,KAAK,YAAY;AACnB,WAAK,mBAAmB;AAAA,IAC1B,WAAWA,SAAQ,GAAG;AACpB,WAAK,aAAa,WAAW,MAAM;AACjC,aAAK,aAAa;AAClB,kBAAU,KAAK,KAAK,IAAI,KAAK,KAAK;AAAA,MACpC,GAAGA,SAAQ,GAAI;AAAA,IACjB,OAAO;AAGL,iBAAW,WAAW,KAAK,KAAK,IAAI,KAAK,KAAK;AAAA,IAChD;AAAA,EACF;AAAA,EACA,yBAAyB;AACvB,SAAK,mBAAmB,QAAQ,aAAW,QAAQ,CAAC;AACpD,SAAK,mBAAmB,SAAS;AAAA,EACnC;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,YAAY;AACnB,mBAAa,KAAK,UAAU;AAC5B,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAA2B,kBAAqB,IAAI,CAAC;AAAA,EACxF;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B;AAAA,EACA,cAAc,OAAO,wBAAwB;AAAA,IAC3C,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,MAAM,OAAO,iBAAiB;AAAA,EAC9B,iBAAiB,OAAO,cAAc;AAAA,EACtC,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,uBAAuB;AAAA,EACvB;AAAA,EACA,iBAAiB,CAAC;AAAA,EAClB,qBAAqB;AAAA,EACrB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA,0BAA0B;AAAA,EAC1B,kBAAkB,IAAI,QAAQ;AAAA,EAC9B,IAAI,UAAU,OAAO;AACnB,UAAM,UAAU,UAAU,KAAK;AAC/B,QAAI,KAAK,aAAa,SAAS;AAC7B,WAAK,WAAW;AAChB,WAAK,gBAAgB,KAAK,OAAO;AAAA,IACnC;AAAA,EACF;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW;AAAA,EACX,IAAI,UAAU,OAAO;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW;AAAA,EACX,IAAI,YAAY,OAAO;AACrB,UAAM,oBAAoB,MAAM,IAAI,eAAa,aAAa,SAAS,CAAC;AACxE,SAAK,aAAa,CAAC,GAAG,mBAAmB,GAAG,yBAAyB;AAAA,EACvE;AAAA,EACA,qBAAqB;AAAA,EACrB;AAAA,EACA,MAAM;AAAA,EACN,YAAY,CAAC;AAAA,EACb,UAAU;AAAA,EACV,aAAa,CAAC,GAAG,yBAAyB;AAAA,EAC1C,WAAW,IAAI,QAAQ;AAAA,EACvB,WAAW;AACT,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,SAAK,gBAAgB,SAAS;AAC9B,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,QAAI,KAAK,WAAW;AAClB;AAAA,IACF;AACA,QAAI,CAAC,KAAK,QAAQ,GAAG;AACnB,WAAK,YAAY;AACjB,WAAK,gBAAgB,KAAK,IAAI;AAC9B,WAAK,IAAI,cAAc;AAAA,IACzB;AAEA,QAAI,KAAK,UAAU,KAAK,WAAW,KAAK,QAAQ,cAAc,KAAK,QAAQ,WAAW,aAAa,MAAM,OAAO;AAC9G,WAAK,QAAQ,WAAW,aAAa,KAAK;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,OAAO;AACL,QAAI,CAAC,KAAK,WAAW;AACnB;AAAA,IACF;AACA,SAAK,YAAY;AACjB,SAAK,gBAAgB,KAAK,KAAK;AAC/B,SAAK,IAAI,cAAc;AAAA,EACzB;AAAA,EACA,oBAAoB;AAClB,SAAK,aAAa;AAClB,SAAK,IAAI,cAAc;AACvB,YAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,WAAK,eAAe;AACpB,WAAK,wBAAwB;AAAA,IAC/B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,QAAI,KAAK,UAAU,KAAK,WAAW,KAAK,QAAQ,YAAY;AAC1D,WAAK,QAAQ,WAAW,eAAe;AAAA,IACzC;AAAA,EACF;AAAA,EACA,iBAAiB,UAAU;AACzB,SAAK,qBAAqB,iBAAiB,QAAQ;AACnD,SAAK,aAAa;AAElB,SAAK,IAAI,cAAc;AAAA,EACzB;AAAA,EACA,iBAAiB,QAAQ;AACvB,SAAK,SAAS;AACd,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,eAAe,OAAO;AACpB,QAAI,CAAC,KAAK,oBAAoB;AAC5B;AAAA,IACF;AACA,UAAM,SAAS,gBAAgB,KAAK;AACpC,QAAI,CAAC,KAAK,OAAO,cAAc,SAAS,MAAM,KAAK,KAAK,cAAc,MAAM;AAC1E,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,0BAA0B;AACxB,QAAI,KAAK,QAAQ,GAAG;AAClB,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,eAAe;AACb,SAAK,YAAY,iCACZ,KAAK,wBAAwB,KAAK,kBAAkB,IADxC;AAAA,MAEf,CAAC,GAAG,KAAK,OAAO,cAAc,KAAK,kBAAkB,EAAE,GAAG;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,wBAAwB,OAAO;AAC7B,UAAM,SAAS,CAAC;AAIhB,UAAM,UAAU,UAAU,OAAO,MAAM,MAAM,KAAK,IAAI,CAAC;AACvD,YAAQ,QAAQ,eAAa,OAAO,SAAS,IAAI,IAAI;AACrD,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,SAAS,6BAA6B,IAAI,KAAK;AACxD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU,GAAG;AAAA,MAChE;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,eAAe,OAAO;AAC7B,SAAO,iBAAiB,cAAc,QAAQ,UAAU,MAAM,CAAC,SAAS,KAAK;AAC/E;AAMA,IAAM,qBAAN,MAAM,4BAA2B,uBAAuB;AAAA;AAAA,EAEtD;AAAA,EACA,eAAe;AAAA,EACf;AAAA,EACA,UAAU;AAAA,EACV,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB,UAAU;AAAA,EACV;AAAA,EACA,gBAAgB,IAAI,aAAa;AAAA,EACjC,cAAc;AACZ,UAAM,kBAAkB;AAAA,EAC1B;AAAA,EACA,sBAAsB;AACpB,WAAO,iCACF,MAAM,oBAAoB,IADxB;AAAA,MAEL,gBAAgB,CAAC,WAAW,MAAM,KAAK,cAAc;AAAA,MACrD,cAAc,CAAC,kBAAkB,MAAM,KAAK,YAAY;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,IAClC,UAAU;AAAA,IACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,oBAAoB,IAAI,OAAO;AAAA,MAChD;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO,CAAC,GAAG,kBAAkB,OAAO;AAAA,MACpC,cAAc,CAAC,GAAG,yBAAyB,cAAc;AAAA,MACzD,gBAAgB,CAAC,GAAG,cAAc,gBAAgB;AAAA,MAClD,SAAS,CAAC,GAAG,oBAAoB,SAAS;AAAA,MAC1C,WAAW,CAAC,GAAG,sBAAsB,WAAW;AAAA,MAChD,QAAQ,CAAC,GAAG,mBAAmB,QAAQ;AAAA,MACvC,SAAS,CAAC,GAAG,oBAAoB,SAAS;AAAA,MAC1C,iBAAiB,CAAC,GAAG,4BAA4B,iBAAiB;AAAA,MAClE,iBAAiB,CAAC,GAAG,4BAA4B,iBAAiB;AAAA,MAClE,kBAAkB,CAAC,GAAG,6BAA6B,kBAAkB;AAAA,MACrE,cAAc,CAAC,GAAG,yBAAyB,cAAc;AAAA,MACzD,oBAAoB,CAAC,GAAG,+BAA+B,sBAAsB,gBAAgB;AAAA,MAC7F,yBAAyB,CAAC,GAAG,2BAA2B,2BAA2B,gBAAgB;AAAA,MACnG,gBAAgB;AAAA,IAClB;AAAA,IACA,SAAS;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,UAAU,CAAC,WAAW;AAAA,IACtB,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,4BAA4B;AAAA,MAC9B;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAM,4BAA2B,uBAAuB;AAAA,EACtD,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB;AAAA,EACA,mBAAmB,CAAC;AAAA,EACpB,UAAU;AACR,WAAO,eAAe,KAAK,OAAO;AAAA,EACpC;AAAA,EACA,eAAe;AACb,UAAM,gBAAgB,KAAK,WAAW,cAAc,KAAK,OAAO;AAChE,SAAK,YAAY,iCACZ,KAAK,wBAAwB,KAAK,kBAAkB,IADxC;AAAA,MAEf,CAAC,GAAG,KAAK,OAAO,cAAc,KAAK,kBAAkB,EAAE,GAAG;AAAA,MAC1D,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,OAAO,EAAE,GAAG;AAAA,IACvC;AACA,SAAK,mBAAmB;AAAA,MACtB,iBAAiB,CAAC,CAAC,KAAK,WAAW,CAAC,gBAAgB,KAAK,UAAU;AAAA,MACnE,iCAAiC,KAAK;AAAA,IACxC;AAAA,EACF;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,2BAA2B,mBAAmB;AAC5D,cAAQ,oCAAoC,kCAAqC,sBAAsB,mBAAkB,IAAI,qBAAqB,mBAAkB;AAAA,IACtK;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,IAC1B,UAAU,CAAC,oBAAoB;AAAA,IAC/B,UAAU,CAAI,0BAA0B;AAAA,IACxC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,WAAW,qBAAqB,GAAG,CAAC,uBAAuB,IAAI,sBAAsB,IAAI,GAAG,uBAAuB,UAAU,kBAAkB,6BAA6B,2BAA2B,gCAAgC,2BAA2B,sBAAsB,GAAG,CAAC,GAAG,eAAe,GAAG,eAAe,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,0BAA0B,+BAA+B,CAAC;AAAA,IAC7f,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,WAAW,GAAG,2CAA2C,GAAG,IAAI,eAAe,GAAG,GAAM,sBAAsB;AACjH,QAAG,WAAW,uBAAuB,SAAS,uEAAuE,QAAQ;AAC3H,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,eAAe,MAAM,CAAC;AAAA,QAClD,CAAC,EAAE,UAAU,SAAS,4DAA4D;AAChF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,KAAK,CAAC;AAAA,QAClC,CAAC,EAAE,kBAAkB,SAAS,kEAAkE,QAAQ;AACtG,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,iBAAiB,MAAM,CAAC;AAAA,QACpD,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,6BAA6B,IAAI,MAAM,EAAE,2BAA2B,IAAI,QAAQ,EAAE,gCAAgC,IAAI,UAAU,EAAE,2BAA2B,IAAI,uBAAuB,EAAE,wBAAwB,IAAI,oBAAoB;AAAA,MAC1P;AAAA,IACF;AAAA,IACA,cAAc,CAAC,eAAkB,qBAAqB,wBAAwB,gBAAmB,iCAAiC,iBAAoB,2BAA2B;AAAA,IACjL,eAAe;AAAA,IACf,MAAM;AAAA,MACJ,WAAW,CAAC,aAAa;AAAA,IAC3B;AAAA,IACA,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,YAAY,CAAC,aAAa;AAAA,MAC1B,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkCV,qBAAqB;AAAA,MACrB,SAAS,CAAC,eAAe,wBAAwB,gBAAgB,eAAe;AAAA,IAClF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,oBAAoB,kBAAkB;AAAA,IAChD,SAAS,CAAC,oBAAoB,kBAAkB;AAAA,EAClD,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,kBAAkB;AAAA,EAC9B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,oBAAoB,kBAAkB;AAAA,MAChD,SAAS,CAAC,oBAAoB,kBAAkB;AAAA,IAClD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC/sBH,IAAMC,OAAM,CAAC,GAAG;AAChB,IAAM,MAAM,QAAM,CAAC,EAAE;AACrB,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,QAAQ;AAAA,EACtC;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,IAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,gBAAgB,CAAC;AACtG,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,eAAe,MAAS;AACtC,IAAG,UAAU;AACb,IAAG,WAAc,gBAAgB,GAAG,KAAK,2BAA2B,OAAO,MAAM,CAAC;AAClF,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,QAAQ,EAAE,iCAAoC,gBAAgB,GAAG,KAAK,OAAO,eAAe,CAAC;AAAA,EAC9I;AACF;AACA,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,OAAO;AAAA,EACrC;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,gBAAgB,CAAC;AACtG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,OAAO;AAAA,EACxD;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,UAAU,GAAG,WAAW,CAAC;AAC5B,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,qBAAqB,IAAI;AAC/B,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,UAAU,kBAAkB,EAAE,WAAW,OAAO,YAAY,KAAK;AAAA,EACjF;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,gBAAgB,CAAC;AACpG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,kBAAkB,OAAO,cAAc;AACrD,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,YAAY,IAAI;AAAA,EACjE;AACF;AACA,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB;AAAA,EACA,SAAS;AAAA,EACT,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,WAAW,IAAI,QAAQ;AAAA,EACvB,mBAAmB,OAAO;AACxB,SAAK,gBAAgB;AACrB,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,UAAU,QAAQ;AAChB,SAAK,SAAS;AACd,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,eAAe,aAAa;AAC1B,SAAK,cAAc;AACnB,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,YAAY,KAAK;AACf,SAAK,MAAM;AAAA,EACb;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAwB,kBAAqB,iBAAiB,CAAC;AAAA,EAClG;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,WAAW,CAAC,GAAG,eAAe;AAAA,IAC9B,UAAU;AAAA,IACV,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,6BAA6B,IAAI,WAAW,SAAS,EAAE,6BAA6B,IAAI,WAAW,SAAS,EAAE,2BAA2B,IAAI,WAAW,OAAO,EAAE,+BAA+B,IAAI,WAAW,YAAY,EAAE,8BAA8B,IAAI,eAAe,IAAI,MAAM,EAAE,2BAA2B,IAAI,aAAa;AAAA,MACvV;AAAA,IACF;AAAA,IACA,UAAU,CAAC,YAAY;AAAA,IACvB,oBAAoBA;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,6BAA6B,IAAI,KAAK;AACvD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,qCAAqC;AAAA,QACrC,qCAAqC;AAAA,QACrC,mCAAmC;AAAA,QACnC,uCAAuC;AAAA,QACvC,sCAAsC;AAAA,QACtC,mCAAmC;AAAA,MACrC;AAAA,MACA,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,wBAAwB;AAC9B,IAAM,qBAAqB;AAAA,EACzB,MAAM;AAAA,EACN,OAAO;AACT;AACA,IAAI,mBAAmB,MAAM;AAC3B,MAAI;AACJ,MAAI,0BAA0B,CAAC;AAC/B,MAAI,+BAA+B,CAAC;AACpC,MAAI;AACJ,MAAI,2BAA2B,CAAC;AAChC,MAAI,gCAAgC,CAAC;AACrC,MAAI;AACJ,MAAI,8BAA8B,CAAC;AACnC,MAAI,mCAAmC,CAAC;AACxC,MAAI;AACJ,MAAI,4BAA4B,CAAC;AACjC,MAAI,iCAAiC,CAAC;AACtC,SAAO,MAAMC,iBAAgB;AAAA,IAC3B,OAAO;AACL,YAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,uBAAO,OAAO,IAAI,IAAI;AAC1F,8BAAwB,CAAC,WAAW,CAAC;AACrC,+BAAyB,CAAC,WAAW,CAAC;AACtC,kCAA4B,CAAC,WAAW,CAAC;AACzC,gCAA0B,CAAC,WAAW,CAAC;AACvC,mBAAa,MAAM,MAAM,uBAAuB;AAAA,QAC9C,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,eAAe;AAAA,UAC3B,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,YAAY;AAAA,UAClB;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,yBAAyB,4BAA4B;AACxD,mBAAa,MAAM,MAAM,wBAAwB;AAAA,QAC/C,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,gBAAgB;AAAA,UAC5B,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,aAAa;AAAA,UACnB;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,0BAA0B,6BAA6B;AAC1D,mBAAa,MAAM,MAAM,2BAA2B;AAAA,QAClD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,mBAAmB;AAAA,UAC/B,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,gBAAgB;AAAA,UACtB;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,6BAA6B,gCAAgC;AAChE,mBAAa,MAAM,MAAM,yBAAyB;AAAA,QAChD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,KAAK,SAAO,iBAAiB;AAAA,UAC7B,KAAK,SAAO,IAAI;AAAA,UAChB,KAAK,CAAC,KAAK,UAAU;AACnB,gBAAI,cAAc;AAAA,UACpB;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,2BAA2B,8BAA8B;AAC5D,UAAI,UAAW,QAAO,eAAe,MAAM,OAAO,UAAU;AAAA,QAC1D,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,YAAY,kBAAkB,MAAM,yBAAyB,KAAK;AAAA,IAClE,cAAc,kBAAkB,MAAM,4BAA4B,GAAG,kBAAkB,MAAM,0BAA0B,CAAC,CAAC;AAAA,IACzH,qBAAqB,kBAAkB,MAAM,6BAA6B,GAAG;AAAA,IAC7E,gBAAgB,kBAAkB,MAAM,6BAA6B,kBAAkB;AAAA,IACvF,gBAAgB,kBAAkB,MAAM,gCAAgC,GAAG;AAAA,IAC3E,cAAc,kBAAkB,MAAM,2BAA2B,KAAK;AAAA,IACtE,OAAO,kBAAkB,MAAM,8BAA8B,GAAG;AAAA,IAChE,WAAW,IAAI,QAAQ;AAAA,IACvB,gBAAgB,IAAI,QAAQ;AAAA,IAC5B,mBAAmB,YAAY;AAC7B,aAAO,KAAK,cAAc,KAAK,OAAO,aAAW,cAAc,OAAO,GAAG,IAAI,WAAS,MAAM,UAAU,CAAC,CAAC;AAAA,IAC1G;AAAA,IACA,YAAY,iBAAiB,gBAAgB;AAC3C,WAAK,kBAAkB;AACvB,WAAK,iBAAiB;AACtB,WAAK,MAAM,KAAK,eAAe;AAC/B,WAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,aAAK,MAAM;AAAA,MACb,CAAC;AAAA,IACH;AAAA,IACA,YAAY,SAAS;AACnB,WAAK,cAAc,KAAK,OAAO;AAAA,IACjC;AAAA,IACA,cAAc;AACZ,WAAK,cAAc,SAAS;AAC5B,WAAK,SAAS,KAAK,IAAI;AACvB,WAAK,SAAS,SAAS;AAAA,IACzB;AAAA,IACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,aAAO,KAAK,qBAAqBA,kBAAoB,kBAAqB,eAAe,GAAM,kBAAqB,cAAc,CAAC;AAAA,IACrI;AAAA,IACA,OAAO,OAAyB,kBAAkB;AAAA,MAChD,MAAMA;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,MAC/B,WAAW,CAAC,GAAG,UAAU;AAAA,MACzB,UAAU;AAAA,MACV,cAAc,SAAS,6BAA6B,IAAI,KAAK;AAC3D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,uBAAuB,IAAI,aAAa,YAAY,EAAE,qBAAqB,IAAI,aAAa,UAAU,EAAE,mBAAmB,IAAI,aAAa,QAAQ,EAAE,gBAAgB,IAAI,QAAQ,KAAK;AAAA,QACxM;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,WAAW,CAAC,GAAG,aAAa,aAAa,gBAAgB;AAAA,QACzD,YAAY;AAAA,QACZ,mBAAmB,CAAC,GAAG,qBAAqB,qBAAqB,gBAAgB;AAAA,QACjF,eAAe;AAAA,QACf,cAAc;AAAA,QACd,aAAa,CAAC,GAAG,eAAe,eAAe,gBAAgB;AAAA,MACjE;AAAA,MACA,UAAU,CAAC,QAAQ;AAAA,MACnB,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF,GAAG;AAAA,CACF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,+BAA+B;AAAA,QAC/B,6BAA6B;AAAA,QAC7B,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,MAC1B;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,eAAe;AAAA,EACf,kBAAkB,aAAa;AAAA,EAC/B,iBAAiB;AAAA,EACjB,aAAa,IAAI,QAAQ;AAAA,EACzB;AAAA,EACA;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,KAAK,sBAAsB,SAAY,UAAU,KAAK,iBAAiB,IAAI,CAAC,CAAC,KAAK,iBAAiB;AAAA,EAC5G;AAAA,EACA,SAAS;AAAA,EACT,kBAAkB;AAAA,EAClB,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa,CAAC;AAAA,EACd;AAAA,EACA,IAAI,cAAc,OAAO;AACvB,SAAK,eAAe;AACpB,SAAK,oBAAoB,kBAAkB,KAAK;AAAA,MAC9C,QAAQ,KAAK;AAAA,MACb,aAAa,KAAK;AAAA,IACpB,CAAC;AACD,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,eAAe,KAAK,YAAY;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,iBAAiB,OAAO;AAC1B,QAAI,iBAAiB,mBAAmB,iBAAiB,SAAS;AAChE,WAAK,kBAAkB;AACvB,WAAK,iBAAiB;AACtB,WAAK,aAAa;AAAA,IACpB,WAAW,iBAAiB,iBAAiB;AAC3C,WAAK,kBAAkB,MAAM;AAC7B,WAAK,iBAAiB;AACtB,WAAK,aAAa;AAAA,IACpB,OAAO;AACL,WAAK,iBAAiB;AACtB,WAAK,kBAAkB;AACvB,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,eAAe;AACb,SAAK,gBAAgB,YAAY;AAEjC,QAAI,KAAK,mBAAmB,KAAK,gBAAgB,eAAe;AAC9D,WAAK,kBAAkB,KAAK,gBAAgB,cAAc,KAAK,UAAU,IAAI,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AAC1H,YAAI,CAAC,KAAK,iBAAiB;AACzB,eAAK,mBAAmB;AAAA,QAC1B;AACA,aAAK,UAAU;AACf,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY;AACV,SAAK,SAAS,KAAK,iBAAiB,KAAK,cAAc;AACvD,SAAK,WAAW,KAAK,YAAY,KAAK,MAAM;AAC5C,SAAK,oBAAoB,kBAAkB,KAAK;AAAA,MAC9C,QAAQ,KAAK;AAAA,MACb,aAAa,KAAK;AAAA,IACpB,CAAC;AACD,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,mBAAmB,CAAC,CAAC,KAAK,QAAQ;AAC3D,WAAK,oBAAoB,UAAU,KAAK,MAAM;AAAA,IAChD;AAAA,EACF;AAAA,EACA,iBAAiB,gBAAgB;AAC/B,QAAI;AACJ,QAAI,mBAAmB,aAAa,KAAK,sBAAsB,WAAW,SAAS,GAAG;AACpF,eAAS;AAAA,IACX,WAAW,mBAAmB,WAAW,KAAK,sBAAsB,SAAS,GAAG;AAC9E,eAAS;AAAA,IACX,WAAW,mBAAmB,gBAAgB,mBAAmB,aAAa,KAAK,sBAAsB,SAAS,GAAG;AACnH,eAAS;AAAA,IACX,WAAW,mBAAmB,aAAa,KAAK,sBAAsB,OAAO,GAAG;AAC9E,eAAS;AAAA,IACX,OAAO;AACL,eAAS;AAAA,IACX;AACA,WAAO;AAAA,EACT;AAAA,EACA,sBAAsB,aAAa,YAAY;AAC7C,QAAI,CAAC,KAAK,iBAAiB;AACzB,aAAO;AAAA,IACT,OAAO;AACL,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,cAAQ,CAAC,CAAC,SAAS,CAAC,CAAC,aAAa,aAAa,KAAK,gBAAgB,SAAS,UAAU,IAAI,WAAW;AAAA,IACxG;AAAA,EACF;AAAA,EACA,YAAY,QAAQ;AAClB,YAAQ,QAAQ;AAAA,MACd,KAAK;AACH,eAAO,CAAC,KAAK,mBAAmB,KAAK,gBAAgB,KAAK,cAAc;AAAA,MAC1E,KAAK;AACH,eAAO,KAAK,mBAAmB;AAAA,MACjC,KAAK;AACH,eAAO,KAAK,gBAAgB;AAAA,MAC9B,KAAK;AACH,eAAO,KAAK,gBAAgB;AAAA,MAC9B;AACE,eAAO;AAAA,IACX;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,iBAAiB;AACxB,YAAM,SAAS,KAAK,gBAAgB,UAAU,CAAC;AAC/C,UAAI,eAAe;AACnB,iBAAW,OAAO,QAAQ;AACxB,YAAI,OAAO,eAAe,GAAG,GAAG;AAC9B,yBAAe,OAAO,GAAG,IAAI,KAAK,QAAQ,KAAK,KAAK,aAAa,KAAK,QAAQ,IAAI,GAAG,KAAK,KAAK,WAAW,UAAU,GAAG,KAAK,KAAK,iBAAiB,aAAa,KAAK,QAAQ,IAAI,GAAG,KAAK,KAAK,iBAAiB,WAAW,UAAU,GAAG;AAAA,QACxO;AACA,YAAI,cAAc;AAChB;AAAA,QACF;AAAA,MACF;AACA,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAAA,EACA,kBAAkB,YAAY;AAC5B,gBAAY,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AAC3D,UAAI,CAAC,KAAK,iBAAiB;AACzB,aAAK,mBAAmB;AACxB,aAAK,UAAU;AACf,aAAK,IAAI,aAAa;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB,OAAO,qBAAqB;AAAA,IAChD,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,kBAAkB,OAAO,iBAAiB;AAAA,IACxC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,KAAK,MAAM,qBAAqB;AAC1C,SAAK,MAAM;AACX,SAAK,sBAAsB;AAC3B,SAAK,kBAAkB,KAAK,aAAa,KAAK,IAAI,YAAU,KAAK,WAAW,OAAO,MAAM,CAAC,CAAC;AAC3F,SAAK,kBAAkB,KAAK,iBAAiB,mBAAmB,YAAY,CAAC;AAC7E,SAAK,kBAAkB,KAAK,iBAAiB,mBAAmB,mBAAmB,EAAE,KAAK,OAAO,MAAM,KAAK,sBAAsB,MAAS,CAAC,CAAC;AAAA,EAC/I;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,qBAAqB,YAAY;AACnC,WAAK,mBAAmB;AACxB,WAAK,UAAU;AAAA,IACjB,WAAW,gBAAgB,gBAAgB,cAAc,iBAAiB;AACxE,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,mBAAmB,CAAC,KAAK,gBAAgB;AACjD,UAAI,KAAK,kCAAkC,sBAAsB;AAC/D,aAAK,mBAAmB,KAAK,uBAAuB;AAAA,MACtD,OAAO;AACL,aAAK,mBAAmB,KAAK;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAA2B,kBAAqB,iBAAiB,GAAM,kBAAuB,aAAa,GAAM,kBAAuB,mBAAmB,CAAC;AAAA,EAC/L;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,IAC/B,gBAAgB,SAAS,sCAAsC,IAAI,KAAK,UAAU;AAChF,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,WAAW,CAAC;AAAA,MAC1C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,yBAAyB,GAAG;AAAA,MAC/E;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,uBAAuB;AAAA,IACtC,QAAQ;AAAA,MACN,cAAc;AAAA,MACd,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,mBAAmB,CAAC,GAAG,qBAAqB,qBAAqB,gBAAgB;AAAA,MACjF,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,MACrE,kBAAkB;AAAA,IACpB;AAAA,IACA,UAAU,CAAC,eAAe;AAAA,IAC1B,UAAU,CAAI,mBAAmB,CAAC,mBAAmB,CAAC,GAAM,oBAAoB;AAAA,IAChF,oBAAoBD;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,6BAA6B,GAAG,CAAC,GAAG,qCAAqC,GAAG,CAAC,GAAG,yBAAyB,iCAAiC,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,QAAQ,OAAO,GAAG,CAAC,GAAG,0BAA0B,+BAA+B,GAAG,CAAC,GAAG,wBAAwB,CAAC;AAAA,IACtS,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa,EAAE;AAClB,QAAG,WAAW,GAAG,+CAA+C,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,+CAA+C,GAAG,GAAG,OAAO,CAAC;AAAA,MAClJ;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,WAAW,IAAI,EAAE;AACtC,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,UAAU,IAAI,EAAE;AAAA,MACvC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,gBAAmB,+BAA+B;AAAA,IACjE,eAAe;AAAA,IACf,MAAM;AAAA,MACJ,WAAW,CAAC,UAAU;AAAA,IACxB;AAAA,IACA,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,YAAY,CAAC,UAAU;AAAA,MACvB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAsBV,WAAW,CAAC,mBAAmB;AAAA,MAC/B,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,cAAc;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG;AAAA,IACF,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,SAAS,cAAc,OAAO;AAC5B,QAAM,OAAO,OAAO,UAAU,WAAW;AAAA,IACvC,MAAM;AAAA,EACR,IAAI;AACJ,SAAO,kCACF,qBACA;AAEP;AACA,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb,IAAI,UAAU,OAAO;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,YAAY,YAAY,KAAK,UAAU,CAAC,CAAC,KAAK,iBAAiB;AAAA,EAC7E;AAAA,EACA,UAAU;AAAA,EACV;AAAA,EACA,IAAI,cAAc,OAAO;AACvB,SAAK,eAAe,cAAc,KAAK;AAAA,EACzC;AAAA;AAAA,EAEA,IAAI,cAAc;AAChB,WAAO,KAAK,iBAAiB,YAAY,KAAK,eAAe,cAAc,KAAK,iBAAiB,iBAAiB,kBAAkB;AAAA,EACtI;AAAA,EACA,eAAe;AAAA,EACf,IAAI,aAAa,OAAO;AACtB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK,eAAe,YAAY,KAAK,aAAa,KAAK,iBAAiB,gBAAgB;AAAA,EACjG;AAAA,EACA,aAAa;AAAA,EACb,IAAI,YAAY,OAAO;AACrB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,cAAc,YAAY,KAAK,YAAY,CAAC,CAAC,KAAK,iBAAiB;AAAA,EACjF;AAAA,EACA,YAAY;AAAA,EACZ,WAAW,IAAI,QAAQ;AAAA,EACvB,kBAAkB,OAAO,iBAAiB;AAAA,IACxC,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,KAAK;AACf,SAAK,MAAM;AACX,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,mBAAmB,WAAW,EAAE,KAAK,OAAO,MAAM,KAAK,YAAY,SAAS,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK,IAAI,aAAa,CAAC;AACrK,WAAK,gBAAgB,mBAAmB,eAAe,EAAE,KAAK,OAAO,MAAM,KAAK,iBAAiB,SAAS,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK,IAAI,aAAa,CAAC;AAC9K,WAAK,gBAAgB,mBAAmB,cAAc,EAAE,KAAK,OAAO,MAAM,KAAK,eAAe,SAAS,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK,IAAI,aAAa,CAAC;AAC3K,WAAK,gBAAgB,mBAAmB,aAAa,EAAE,KAAK,OAAO,MAAM,KAAK,cAAc,SAAS,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK,IAAI,aAAa,CAAC;AAAA,IAC3K;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAyB,kBAAqB,iBAAiB,CAAC;AAAA,EACnG;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,IAC7B,WAAW,CAAC,GAAG,qBAAqB;AAAA,IACpC,UAAU;AAAA,IACV,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,4BAA4B,IAAI,iBAAiB,MAAM,EAAE,4BAA4B,IAAI,WAAW;AAAA,MACrH;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,WAAW,CAAC,GAAG,aAAa,aAAa,gBAAgB;AAAA,MACzD,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,cAAc;AAAA,MACd,aAAa,CAAC,GAAG,eAAe,eAAe,gBAAgB;AAAA,IACjE;AAAA,IACA,UAAU,CAAC,aAAa;AAAA,IACxB,oBAAoBA;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,cAAc,IAAI,GAAG,yBAAyB,GAAG,gBAAgB,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,UAAU,SAAS,CAAC;AAAA,IACrI,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO;AAC5B,QAAG,aAAa,CAAC;AACjB,QAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,QAAQ,CAAC;AAC7E,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,0BAA0B,IAAI,SAAS,EAAE,0BAA0B,IAAI,UAAU;AAChG,QAAG,YAAY,OAAO,IAAI,KAAK;AAC/B,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,iBAAiB,IAAI,EAAE;AAAA,MAC9C;AAAA,IACF;AAAA,IACA,cAAc,CAAC,gBAAmB,iCAAiC,oBAAoB,cAAmB,eAAe;AAAA,IACzH,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAYV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,oCAAoC;AAAA,QACpC,oCAAoC;AAAA,MACtC;AAAA,MACA,SAAS,CAAC,gBAAgB,oBAAoB,YAAY;AAAA,IAC5D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,IAC7B,WAAW,CAAC,GAAG,gBAAgB;AAAA,IAC/B,UAAU,CAAC,aAAa;AAAA,IACxB,oBAAoBA;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,WAAW,CAAC,GAAG,eAAe;AAAA,IAC9B,UAAU,CAAC,YAAY;AAAA,IACvB,oBAAoBA;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,6BAA6B,IAAI,KAAK;AACvD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,qBAAqB,sBAAsB,wBAAwB,qBAAqB,oBAAoB;AAAA,IACvI,SAAS,CAAC,cAAc,iBAAiB,qBAAqB,sBAAsB,wBAAwB,qBAAqB,oBAAoB;AAAA,EACvJ,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,sBAAsB,wBAAwB,YAAY;AAAA,EACtE,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,qBAAqB,sBAAsB,wBAAwB,qBAAqB,oBAAoB;AAAA,MACvI,SAAS,CAAC,cAAc,iBAAiB,qBAAqB,sBAAsB,wBAAwB,qBAAqB,oBAAoB;AAAA,IACvJ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["delay", "_c0", "NzFormDirective"]}