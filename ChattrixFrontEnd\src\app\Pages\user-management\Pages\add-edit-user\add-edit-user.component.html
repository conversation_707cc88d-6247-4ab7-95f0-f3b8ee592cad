<div class="add-edit-user-container">
  <!-- Form Section with Integrated Header -->
  <div class="form-section">
    <mat-card class="form-card">
      <!-- Integrated Header Section -->
      <div class="integrated-header">
        <div class="header-content">
          <h1 class="page-title">{{ formTitle }}</h1>
          <p class="page-subtitle">
            {{
              isEditMode
                ? "Update user information and permissions"
                : "Create a new user account with appropriate permissions"
            }}
          </p>
        </div>
      </div>

      <form [formGroup]="userForm" (ngSubmit)="onSubmit()" novalidate>
        <!-- Basic Information Section -->
        <div class="form-section-group">
          <h3 class="section-title">Basic Information</h3>
          <div class="form-row">
            <!-- Full Name -->
            <mat-form-field class="form-field">
              <mat-label>Full Name</mat-label>
              <input
                matInput
                formControlName="fullName"
                placeholder="Enter full name"
              />
              <mat-error *ngIf="userForm.get('fullName')?.hasError('required')">
                Full name is required
              </mat-error>
              <mat-error
                *ngIf="userForm.get('fullName')?.hasError('minlength')"
              >
                Full name must be at least 2 characters
              </mat-error>
            </mat-form-field>

            <!-- Email -->
            <mat-form-field class="form-field">
              <mat-label>Email Address</mat-label>
              <input
                matInput
                formControlName="email"
                placeholder="Enter email address"
                type="email"
              />
              <mat-error *ngIf="userForm.get('email')?.hasError('required')">
                Email is required
              </mat-error>
              <mat-error *ngIf="userForm.get('email')?.hasError('email')">
                Please enter a valid email address
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <!-- Phone Number -->
            <mat-form-field class="form-field">
              <mat-label>Phone Number</mat-label>
              <input
                matInput
                formControlName="phoneNumber"
                placeholder="Enter phone number"
              />
            </mat-form-field>

            <!-- Status -->
            <mat-form-field class="form-field">
              <mat-label>Status</mat-label>
              <mat-select formControlName="isActive">
                <mat-option [value]="true">Active</mat-option>
                <mat-option [value]="false">Inactive</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>

        <!-- Password Section (Only for Add Mode) -->
        <div class="form-section-group" *ngIf="!isEditMode">
          <h3 class="section-title">Password</h3>
          <div class="form-row">
            <!-- Password -->
            <mat-form-field class="form-field">
              <mat-label>Password</mat-label>
              <input
                matInput
                formControlName="password"
                placeholder="Enter password"
                type="password"
              />
              <mat-error *ngIf="userForm.get('password')?.hasError('required')">
                Password is required
              </mat-error>
              <mat-error
                *ngIf="userForm.get('password')?.hasError('minlength')"
              >
                Password must be at least 6 characters
              </mat-error>
            </mat-form-field>

            <!-- Confirm Password -->
            <mat-form-field class="form-field">
              <mat-label>Confirm Password</mat-label>
              <input
                matInput
                formControlName="confirmPassword"
                placeholder="Confirm password"
                type="password"
              />
              <mat-error
                *ngIf="userForm.get('confirmPassword')?.hasError('required')"
              >
                Please confirm your password
              </mat-error>
              <mat-error
                *ngIf="
                  userForm.get('confirmPassword')?.hasError('passwordMismatch')
                "
              >
                Passwords do not match
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <!-- Roles Section -->
        <div class="form-section-group">
          <h3 class="section-title">Roles & Permissions</h3>
          <div class="form-row">
            <mat-form-field class="form-field full-width">
              <mat-label>User Roles</mat-label>
              <mat-select formControlName="roles" multiple>
                <mat-option
                  *ngFor="let role of availableRoles"
                  [value]="role.value"
                >
                  {{ role.label }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="userForm.get('roles')?.hasError('required')">
                At least one role is required
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <!-- Description Section -->
        <div class="form-section-group">
          <h3 class="section-title">Additional Information</h3>
          <div class="form-row">
            <mat-form-field class="form-field full-width">
              <mat-label>Description</mat-label>
              <textarea
                matInput
                formControlName="description"
                placeholder="Enter user description or notes"
                rows="4"
              ></textarea>
            </mat-form-field>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <button
            type="button"
            mat-button
            class="cancel-btn"
            (click)="onCancel()"
          >
            Cancel
          </button>
          <button
            type="submit"
            mat-raised-button
            color="primary"
            class="submit-btn"
            [disabled]="isLoading"
          >
            <mat-spinner diameter="20" *ngIf="isLoading"></mat-spinner>
            <mat-icon *ngIf="!isLoading">{{
              isEditMode ? "save" : "person_add"
            }}</mat-icon>
            {{ submitButtonText }}
          </button>
        </div>
      </form>
    </mat-card>
  </div>
</div>
